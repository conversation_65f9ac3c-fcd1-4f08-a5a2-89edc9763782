# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Package Manager**: This project uses `pnpm` as the package manager.

```bash
# Install dependencies
pnpm install

# Start development server (http://localhost:3000)
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview

# Lint code (using @antfu/eslint-config)
pnpm lint

# Run type checking
pnpm typecheck
```

## Architecture Overview

This is a **Nuxt 4** application with a modern TypeScript-first architecture using:

- **Framework**: Nuxt 4 with TypeScript
- **UI Components**: Shadcn/ui with reka-ui components
- **Styling**: TailwindCSS v4 with custom animations
- **Directory Structure**: Custom `srcDir: 'app/'` configuration

### Key Architecture Patterns

**API Client Pattern**: The application follows a structured API client architecture:

- All API calls use composables: `useAPI()`, `useLazyAPI()`, and `useAPICall()`
- Repository pattern with dedicated repository files in `app/repositories/`
- Type-safe API responses using interfaces in `shared/types/api.d.ts`

**Data Fetching Strategy**:

- **Pages own their data**: Pages are responsible for their own data fetching logic
- **Repository pattern**: All API calls are abstracted into repository methods
- **URL-driven state**: Query parameters are the single source of truth for bookmarkable state
- Use `useAsyncData` with repository methods for SSR-compatible data fetching
- Use `useQueryParams` for reactive URL parameter management

**Component Architecture**:

- UI components located in `app/components/ui/` (auto-imported by Nuxt)
- Shadcn/ui components are used for consistent design system
- Custom components follow Vue 3 Composition API patterns

### Directory Structure

```
app/                    # Main source directory (srcDir)
├── assets/css/         # Global styles
├── components/ui/      # Shadcn/ui components
├── composables/        # Vue composables
├── lib/               # Utility functions
├── plugins/           # Nuxt plugins
├── repositories/      # API repository pattern
└── types/            # TypeScript type definitions

shared/                # Shared utilities and types
├── constants/         # Application constants
├── types/            # Shared TypeScript types
└── utils/           # Shared utility functions

rules/                 # Development guidelines and documentation
```

## Important Development Guidelines

**Data Fetching Pattern** (from `rules/coding-style.mdc`):

- Pages should use `useAsyncData` to call repository methods directly
- Don't create page-specific composables for data fetching
- Use `watch: [queryParams]` to automatically refetch when URL changes
- Repository methods should be simple async functions that return API data

**API Client Usage**:

- Use `useAPI()` for SSR-friendly data fetching
- Use `useLazyAPI()` for non-critical data that can load after navigation
- Use `useAPICall()` for user-triggered actions (forms, buttons)
- All API responses follow a consistent `ApiResponse<T>` structure

**Component Installation**:

```bash
# Add new Shadcn/ui components
pnpm dlx shadcn-vue@latest add [component-name]
```

**Code Quality**:

- ESLint configuration uses @antfu/eslint-config with formatters enabled
- TypeScript strict mode is enabled
- Follow Vue 3 Composition API patterns

## Key Composables

- `useAPI()` - SSR-compatible data fetching
- `useAPICall()` - Direct API calls for actions
- `useQueryParams()` - Reactive URL parameter management
- `useBaseQuery()` - Common query operations (search, pagination)

## Type Safety

The application is fully typed with:

- API response types in `shared/types/api.d.ts`
- Application-specific types in `app/types/`
- Laravel-style pagination support with `LaravelPaginatedResponse<T>`
