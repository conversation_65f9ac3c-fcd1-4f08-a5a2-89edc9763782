# English Game Backend API Documentation

## Base URL

```
https://your-api-domain.com/api
```

## Authentication

Admin endpoints require authentication. Include the admin token in the Authorization header:

```bash
Authorization: Bearer YOUR_ADMIN_TOKEN
```

## Response Format

All API responses follow this standardized format:

**Success Response:**

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ }
}
```

**Error Response:**

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

---

# Course Management API

## List Courses

**Admin:** `GET /admin/courses`
**Public:** `GET /public/courses`

```bash
# Admin - List all courses with pagination
curl -X GET "https://your-api-domain.com/api/admin/courses?per_page=10" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Public - Browse courses
curl -X GET "https://your-api-domain.com/api/public/courses"
```

**Response:**

```json
{
  "success": true,
  "message": "Courses retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "title": "English Basics",
        "description": "Learn fundamental English skills",
        "created_at": "2025-07-20T10:30:00.000000Z",
        "updated_at": "2025-07-20T10:30:00.000000Z",
        "units": [
          {
            "id": 1,
            "title": "Unit 1: Greetings",
            "skill_type": "vocabulary",
            "difficulty": "beginner"
          }
        ]
      }
    ],
    "per_page": 15,
    "total": 1
  }
}
```

## Create Course

**Endpoint:** `POST /admin/courses`

```bash
curl -X POST "https://your-api-domain.com/api/admin/courses" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Advanced English",
    "description": "Master advanced English concepts"
  }'
```

**Response:**

```json
{
  "success": true,
  "message": "Course created successfully",
  "data": {
    "id": 2,
    "title": "Advanced English",
    "description": "Master advanced English concepts",
    "created_at": "2025-07-20T11:00:00.000000Z",
    "updated_at": "2025-07-20T11:00:00.000000Z",
    "units": []
  }
}
```

**Error Response:**

```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "title": ["The title field is required."]
  }
}
```

## Get Course Details

**Admin:** `GET /admin/courses/{id}`
**Public:** `GET /public/courses/{id}`

```bash
curl -X GET "https://your-api-domain.com/api/public/courses/1"
```

**Response:**

```json
{
  "success": true,
  "message": "Course retrieved successfully",
  "data": {
    "id": 1,
    "title": "English Basics",
    "description": "Learn fundamental English skills",
    "created_at": "2025-07-20T10:30:00.000000Z",
    "updated_at": "2025-07-20T10:30:00.000000Z",
    "units": [
      {
        "id": 1,
        "title": "Unit 1: Greetings",
        "assessments": [
          {
            "id": 1,
            "question": "What is 'hello' in English?"
          }
        ]
      }
    ]
  }
}
```

## Update Course

**Endpoint:** `PUT/PATCH /admin/courses/{id}`

```bash
curl -X PUT "https://your-api-domain.com/api/admin/courses/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "English Fundamentals",
    "description": "Updated description"
  }'
```

## Delete Course

**Endpoint:** `DELETE /admin/courses/{id}`

```bash
curl -X DELETE "https://your-api-domain.com/api/admin/courses/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**Response:**

```json
{
  "success": true,
  "message": "Course deleted successfully",
  "data": null
}
```

**Error (Course has units):**

```json
{
  "success": false,
  "message": "Failed to delete course",
  "error": "Cannot delete course that contains units. Please remove or move units first."
}
```

## Duplicate Course

**Endpoint:** `POST /admin/courses/{id}/duplicate`

```bash
curl -X POST "https://your-api-domain.com/api/admin/courses/1/duplicate" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## Course Statistics

**Endpoint:** `GET /admin/courses-with-stats`

```bash
curl -X GET "https://your-api-domain.com/api/admin/courses-with-stats" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

---

# Unit Management API

## List Units

**Admin:** `GET /admin/units`
**Public:** `GET /public/courses/{courseId}/units`

```bash
# Admin - List all units with filters
curl -X GET "https://your-api-domain.com/api/admin/units?course_id=1&skill_type=vocabulary&difficulty=beginner&per_page=20" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Public - Get units by course
curl -X GET "https://your-api-domain.com/api/public/courses/1/units"
```

**Query Parameters:**

- `course_id` - Filter by course
- `skill_type` - vocabulary, grammar, listening, reading, speaking, writing
- `difficulty` - beginner, elementary, intermediate, upper_intermediate, advanced, proficient
- `per_page` - Items per page (max 100)

**Response:**

```json
{
  "success": true,
  "message": "Units retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "course_id": 1,
        "title": "Unit 1: Basic Greetings",
        "description": "Learn basic greeting phrases",
        "skill_type": "vocabulary",
        "difficulty": "beginner",
        "unit_order": 1,
        "course": {
          "id": 1,
          "title": "English Basics"
        },
        "assessments": [
          {
            "id": 1,
            "question": "What is 'hello' in English?"
          }
        ]
      }
    ]
  }
}
```

## Create Unit

**Endpoint:** `POST /admin/units`

```bash
curl -X POST "https://your-api-domain.com/api/admin/units" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "course_id": 1,
    "title": "Unit 2: Numbers",
    "description": "Learn numbers 1-100",
    "skill_type": "vocabulary",
    "difficulty": "beginner",
    "unit_order": 2
  }'
```

**Response:**

```json
{
  "success": true,
  "message": "Unit created successfully",
  "data": {
    "id": 2,
    "course_id": 1,
    "title": "Unit 2: Numbers",
    "description": "Learn numbers 1-100",
    "skill_type": "vocabulary",
    "difficulty": "beginner",
    "unit_order": 2,
    "created_at": "2025-07-20T11:15:00.000000Z",
    "updated_at": "2025-07-20T11:15:00.000000Z"
  }
}
```

## Get Unit Details

**Admin:** `GET /admin/units/{id}`
**Public:** `GET /public/units/{id}`

```bash
curl -X GET "https://your-api-domain.com/api/public/units/1"
```

## Update Unit

**Endpoint:** `PUT/PATCH /admin/units/{id}`

```bash
curl -X PATCH "https://your-api-domain.com/api/admin/units/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Unit 1: Essential Greetings",
    "difficulty": "elementary"
  }'
```

## Delete Unit

**Endpoint:** `DELETE /admin/units/{id}`

```bash
curl -X DELETE "https://your-api-domain.com/api/admin/units/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## Duplicate Unit

**Endpoint:** `POST /admin/units/{id}/duplicate`

```bash
curl -X POST "https://your-api-domain.com/api/admin/units/1/duplicate" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "target_order": 3
  }'
```

## Move Unit to Different Course

**Endpoint:** `PATCH /admin/units/{id}/move-to-course`

```bash
curl -X PATCH "https://your-api-domain.com/api/admin/units/1/move-to-course" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "target_course_id": 2,
    "target_order": 1
  }'
```

## Reorder Unit

**Endpoint:** `PATCH /admin/units/{id}/reorder`

```bash
curl -X PATCH "https://your-api-domain.com/api/admin/units/1/reorder" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "new_order": 5
  }'
```

---

# Assessment Management API

## List Assessments

**Admin:** `GET /admin/assessments/multiple-select`

```bash
curl -X GET "https://your-api-domain.com/api/admin/assessments/multiple-select?per_page=20" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**Response:**

```json
{
  "success": true,
  "message": "Assessments retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "question": "What is 'hello' in English?",
        "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
        "correct_answer_indexes": [0],
        "explanations": ["Common greeting"],
        "assessment": {
          "id": 1,
          "units": [
            {
              "id": 1,
              "title": "Unit 1: Greetings"
            }
          ]
        }
      }
    ]
  }
}
```

## Create Assessment

**Endpoint:** `POST /admin/assessments/multiple-select`

```bash
curl -X POST "https://your-api-domain.com/api/admin/assessments/multiple-select" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "How do you say goodbye in English?",
    "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
    "correct_answer_indexes": [1],
    "explanations": ["Used when leaving"],
    "unit_attachments": [
        {
            "unit_id": 1,
            "assessment_order": 2
        }
    ]
  }'
```

**Response:**

```json
{
  "success": true,
  "message": "Assessment created successfully",
  "data": {
    "id": 2,
    "question": "How do you say goodbye in English?",
    "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
    "correct_answer_indexes": [1],
    "explanations": ["Used when leaving"],
    "created_at": "2025-07-20T11:30:00.000000Z"
  }
}
```

**Validation Error:**

```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "question": ["The question field is required."],
    "answer_list": ["The answer list must contain at least 2 options."]
  }
}
```

## Get Assessment Details

**Admin:** `GET /admin/assessments/multiple-select/{id}`
**Public:** `GET /public/assessments/multiple-select/{id}`

```bash
curl -X GET "https://your-api-domain.com/api/public/assessments/multiple-select/1"
```

## Update Assessment

**Endpoint:** `PUT/PATCH /admin/assessments/multiple-select/{id}`

```bash
curl -X PATCH "https://your-api-domain.com/api/admin/assessments/multiple-select/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "What is the correct greeting in English?",
    "explanations": ["Most common greeting used daily"]
  }'
```

## Delete Assessment

**Endpoint:** `DELETE /admin/assessments/multiple-select/{id}`

```bash
curl -X DELETE "https://your-api-domain.com/api/admin/assessments/multiple-select/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## Attach Assessment to Units

**Endpoint:** `POST /admin/assessments/multiple-select/{id}/attach-units`

```bash
curl -X POST "https://your-api-domain.com/api/admin/assessments/multiple-select/1/attach-units" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "unit_attachments": [
        {
            "unit_id": 2,
            "assessment_order": 1
        },
        {
            "unit_id": 3,
            "assessment_order": 3
        }
    ]
  }'
```

## Detach Assessment from Units

**Endpoint:** `POST /admin/assessments/multiple-select/{id}/detach-units`

```bash
# Detach from specific units
curl -X POST "https://your-api-domain.com/api/admin/assessments/multiple-select/1/detach-units" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "unit_ids": [2, 3]
  }'

# Detach from all units (empty array)
curl -X POST "https://your-api-domain.com/api/admin/assessments/multiple-select/1/detach-units" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "unit_ids": []
  }'
```

---

# CSV Import API

## Import CSV File

**Endpoint:** `POST /admin/import/csv`

```bash
curl -X POST "https://your-api-domain.com/api/admin/import/csv" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "csv_file=@/path/to/your/import.csv" \
  -F "course_id=1"
```

**Response:**

```json
{
  "success": true,
  "message": "CSV import completed successfully",
  "data": {
    "summary": {
      "units_created": 3,
      "assessments_created": 12
    },
    "created_units": [
      {
        "id": 10,
        "title": "Unit 1: Basic Greetings",
        "skill_type": "vocabulary",
        "difficulty": "beginner",
        "unit_order": 1
      },
      {
        "id": 11,
        "title": "Unit 2: Numbers",
        "skill_type": "vocabulary",
        "difficulty": "beginner",
        "unit_order": 2
      }
    ]
  }
}
```

**Error Response:**

```json
{
  "success": false,
  "message": "CSV validation failed",
  "error": "Missing required headers: unit_title, question"
}
```

## Validate CSV (Dry Run)

**Endpoint:** `POST /admin/import/validate-csv`

```bash
curl -X POST "https://your-api-domain.com/api/admin/import/validate-csv" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "csv_file=@/path/to/your/import.csv" \
  -F "course_id=1"
```

**Response:**

```json
{
  "success": true,
  "message": "CSV file is valid and ready for import",
  "data": {
    "total_rows": 10,
    "estimated_units": 3,
    "estimated_assessments": 10,
    "preview": [
      {
        "unit_title": "Unit 1: Basic Greetings",
        "unit_description": "Learn basic greeting phrases",
        "skill_type": "vocabulary",
        "difficulty": "beginner",
        "question": "What is 'hello' in English?",
        "option_1": "Hello",
        "option_2": "Goodbye",
        "correct_answer": "Hello"
      }
    ]
  }
}
```

## Download CSV Template

**Endpoint:** `GET /admin/import/template`

```bash
curl -X GET "https://your-api-domain.com/api/admin/import/template" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -o "import_template.csv"
```

**Response:** CSV file download with headers:

```csv
unit_title,unit_description,skill_type,difficulty,question,option_1,option_2,option_3,option_4,correct_answer,explanation
"Sample Unit","Sample description","vocabulary","beginner","Sample question?","Option A","Option B","Option C","Option D","Option A","Sample explanation"
```

## CSV Format Requirements

**Required Headers:**

- `unit_title` - Title of the unit (max 255 chars)
- `unit_description` - Description (optional, max 2000 chars)
- `skill_type` - One of: vocabulary, grammar, listening, reading, speaking, writing
- `difficulty` - One of: beginner, elementary, intermediate, upper_intermediate, advanced, proficient
- `question` - Assessment question (max 1000 chars)
- `option_1` - First answer option (required, max 500 chars)
- `option_2` - Second answer option (required, max 500 chars)
- `option_3` - Third answer option (optional, max 500 chars)
- `option_4` - Fourth answer option (optional, max 500 chars)
- `correct_answer` - Must match one of the options exactly
- `explanation` - Answer explanation (optional, max 1000 chars)

**File Constraints:**

- Maximum file size: 10MB
- Supported formats: CSV, TXT
- Encoding: UTF-8

---

# Common Error Responses

## Authentication Error

```json
{
  "success": false,
  "message": "Unauthenticated"
}
```

## Validation Error

```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "field_name": ["The field is required."]
  }
}
```

## Not Found Error

```json
{
  "success": false,
  "message": "Resource not found"
}
```

## Server Error

```json
{
  "success": false,
  "message": "Failed to process request",
  "error": "Internal server error details"
}
```

## Rate Limiting

```json
{
  "success": false,
  "message": "Too many requests. Please try again later."
}
```

---

# HTTP Status Codes

- `200` - Success
- `201` - Created successfully
- `400` - Bad request / Validation error
- `401` - Unauthorized / Authentication required
- `403` - Forbidden / Insufficient permissions
- `404` - Resource not found
- `422` - Validation failed
- `429` - Rate limit exceeded
- `500` - Internal server error
