# Nuxt 3 API Client Guide

A comprehensive, production-ready API client for Nuxt 3 applications following current best practices for data fetching, error handling, and TypeScript integration.

## Table of Contents

1. [Overview](#overview)
2. [Installation & Setup](#installation--setup)
3. [Basic Usage](#basic-usage)
4. [API Client Features](#api-client-features)
5. [Composables](#composables)
6. [Authentication](#authentication)
7. [Error Handling](#error-handling)
8. [TypeScript Support](#typescript-support)
9. [Best Practices](#best-practices)
10. [Examples](#examples)

## Overview

This API client provides:

- 🚀 **SSR-ready** data fetching with automatic hydration
- 🔐 **Authentication** handling with automatic token management
- 🛡️ **Error handling** with structured error responses
- 📝 **TypeScript** support with full type safety
- 🔄 **Retry logic** and request/response interceptors
- 📊 **Multiple patterns** for different use cases (SSR, lazy loading, client-side)
- 🏗️ **Modular architecture** with reusable composables

## Installation & Setup

### 1. Environment Configuration

Create a `.env` file in your project root:

```env
# API Configuration
API_BASE_URL=http://localhost:3001/api
APP_URL=http://localhost:3000
API_SECRET=your-secret-key
```

### 2. Runtime Configuration

The Nuxt config is already set up in `nuxt.config.ts`:

```typescript
export default defineNuxtConfig({
  runtimeConfig: {
    apiSecret: process.env.API_SECRET || '',
    public: {
      apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:3001/api',
      appUrl: process.env.APP_URL || 'http://localhost:3000',
    },
  },
})
```

### 3. Auto-imports

All composables are automatically imported thanks to Nuxt's auto-import feature:

- `useAPI()`
- `useLazyAPI()`
- `useAPICall()`
- `useAuth()`
- `useUsers()`

## Basic Usage

### Simple Data Fetching

```vue
<script setup lang="ts">
// SSR-friendly data fetching
const { data: users, pending, error, refresh } = await useAPI<User[]>('/users')
</script>

<template>
  <div>
    <div v-if="pending">
      Loading...
    </div>
    <div v-else-if="error">
      Error: {{ error.message }}
    </div>
    <div v-else>
      <div v-for="user in users" :key="user.id">
        {{ user.name }}
      </div>
    </div>
  </div>
</template>
```

### Lazy Loading

```vue
<script setup lang="ts">
// Won't block navigation, loads after page renders
const { data: stats, pending } = await useLazyAPI <Stats> ('/stats')
</script>
```

### Client-side API Calls

```vue
<script setup lang="ts">
const { post } = useAPICall()

async function createUser(userData: CreateUserData) {
  try {
    const newUser = await post<User>('/users', userData)
    console.log('User created:', newUser)
  }
  catch (error) {
    console.error('Failed to create user:', error)
  }
}
</script>
```

## API Client Features

### Request/Response Interceptors

The API client automatically:

- Adds authentication headers
- Sets common headers (Content-Type, Accept)
- Handles token refresh
- Logs requests in development
- Implements retry logic

### Error Handling

Structured error handling with specific status code responses:

- **401**: Unauthorized (clears token, can redirect to login)
- **403**: Forbidden
- **404**: Not Found
- **422**: Validation Error (includes field-specific errors)
- **429**: Rate Limited
- **5xx**: Server Errors

### Caching

Automatic request deduplication and caching using Nuxt's built-in mechanisms.

## Composables

### `useAPI<T>(endpoint, options)`

Primary composable for SSR-friendly data fetching.

```typescript
const { data, pending, error, refresh } = await useAPI<User[]>('/users', {
  key: 'users',
  query: { page: 1, limit: 10 },
  server: true, // Enable SSR
  transform: response => response.data,
  onResponseError: ({ error }) => console.error(error),
})
```

**Options:**
- `key`: Cache key for deduplication
- `query`: URL query parameters
- `server`: Enable/disable SSR
- `lazy`: Lazy loading
- `transform`: Transform response data
- `default`: Default value

### `useLazyAPI<T>(endpoint, options)`

Lazy loading version that doesn't block navigation:

```typescript
const { data, pending, error } = await useLazyAPI <Stats> ('/stats')
```

### `useAPICall()`

Direct API calls for user interactions:

```typescript
const { call, get, post, put, patch, delete: del } = useAPICall()

// Generic call
const data = await call<ResponseType>('/endpoint', {
  method: 'POST',
  body: { key: 'value' },
  query: { param: 'value' },
})

// HTTP method shortcuts
const users = await get<User[]>('/users')
const user = await post<User>('/users', userData)
const updated = await put<User>('/users/1', updateData)
const patched = await patch<User>('/users/1', patchData)
await del('/users/1')
```

### `useAPIState()`

Utility for managing loading states:

```typescript
const { loading, error, data, execute, reset } = useAPIState()

function loadData() {
  return execute(async () => {
    return await apiCall()
  })
}
```

## Authentication

### `useAuth()` Composable

Complete authentication management:

```typescript
const {
  user, // Current user (readonly ref)
  isAuthenticated, // Computed boolean
  isLoading, // Loading state (readonly ref)
  login, // Login function
  register, // Register function
  logout, // Logout function
  updateProfile, // Update profile function
  initAuth, // Initialize auth state
  hasRole, // Check user role
  hasAnyRole, // Check multiple roles
} = useAuth()
```

### Usage Examples

```typescript
// Login
await login({ email: '<EMAIL>', password: 'password' })

// Register
await register({
  name: 'John Doe',
  email: '<EMAIL>',
  password: 'password',
  password_confirmation: 'password',
})

// Check authentication
if (isAuthenticated.value) {
  console.log('User is logged in:', user.value.name)
}

// Role checking
if (hasRole('admin')) {
  // Admin only functionality
}

if (hasAnyRole(['admin', 'moderator'])) {
  // Admin or moderator functionality
}
```

### Token Management

Tokens are automatically:
- Stored in localStorage
- Added to request headers
- Refreshed when provided by server
- Cleared on logout or auth errors

## Error Handling

### Structured Error Format

```typescript
interface ApiError {
  message: string
  statusCode: number
  data?: any
  errors?: Record<string, string[]> // Validation errors
}
```

### Error Handling Patterns

```typescript
try {
  const data = await apiCall()
}
catch (error: any) {
  console.error('Status:', error.statusCode)
  console.error('Message:', error.data.message)

  // Handle validation errors
  if (error.statusCode === 422 && error.data.errors) {
    Object.entries(error.data.errors).forEach(([field, messages]) => {
      console.error(`${field}: ${messages.join(', ')}`)
    })
  }
}
```

### Global Error Handling

Errors are automatically handled in the plugin, but you can add custom logic:

```typescript
// In a component
const { data, error } = await useAPI('/endpoint', {
  onResponseError: ({ response }) => {
    // Custom error handling
    if (response.status === 403) {
      // Handle forbidden access
    }
  },
})
```

## TypeScript Support

### Type Definitions

```typescript
// API Response wrapper
interface ApiResponse<T> {
  data: T
  message?: string
  status: number
  success: boolean
}

// Paginated responses
interface PaginatedResponse<T> {
  data: T[]
  meta: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
  links: {
    first?: string
    last?: string
    prev?: string
    next?: string
  }
}

// User entity
interface User {
  id: number
  name: string
  email: string
  avatar?: string
  role: string
  createdAt: string
  updatedAt: string
}
```

### Type-safe API Calls

```typescript
// Typed responses
const { data: users } = await useAPI <PaginatedResponse <User>> ('/users')
const { data: user } = await useAPI <User> ('/users/1')

// Typed parameters
const createUser = await useAPICall().post <User> ('/users', {
  name: 'John Doe',
  email: '<EMAIL>',
  password: 'password',
  role: 'user',
})
```

## Best Practices

### 1. Use the Right Composable

- **`useAPI`**: For SSR data that's needed immediately
- **`useLazyAPI`**: For non-critical data that can load after navigation
- **`useAPICall`**: For user-triggered actions (forms, buttons)

### 2. Error Handling

```typescript
// Always handle errors gracefully
const { data, error } = await useAPI('/endpoint')

if (error.value) {
  // Show user-friendly message
  console.error('Failed to load data')
}
```

### 3. Loading States

```typescript
// Show loading indicators
const { pending } = await useAPI('/endpoint')

// In template
<div v-if="pending">Loading...</div>
```

### 4. Key Management

```typescript
// Use descriptive keys for caching
const { data } = await useAPI('/users', {
  key: `users-page-${page}-search-${search}`,
})
```

### 5. Reactive Query Parameters

```typescript
const search = ref('')
const page = ref(1)

const { data, refresh } = await useAPI('/users', {
  query: computed(() => ({
    search: search.value,
    page: page.value,
  })),
})

// Data automatically refetches when search or page changes
```

## Examples

### CRUD Operations

```typescript
const { fetchUsers, createUser, updateUser, deleteUser } = useUsers()

// Read
const { data: users } = await fetchUsers({ page: 1, limit: 10 })

// Create
const newUser = await createUser({
  name: 'John Doe',
  email: '<EMAIL>',
  password: 'password',
  role: 'user',
})

// Update
const updatedUser = await updateUser(1, { name: 'Jane Doe' })

// Delete
await deleteUser(1)
```

### File Uploads

```typescript
const { uploadAvatar } = useUsers()

async function handleFileUpload(file: File, userId: number) {
  try {
    const result = await uploadAvatar(userId, file)
    console.log('Avatar uploaded:', result.url)
  }
  catch (error) {
    console.error('Upload failed:', error)
  }
}
```

### Real-time Data

```typescript
const { useRealtimeUsers } = useUsers()

// Auto-refreshes every 30 seconds
const { data: users, refresh } = useRealtimeUsers(30000)
```

### Pagination

```typescript
const page = ref(1)
const limit = ref(10)

const { data: users, pending } = await useAPI('/users', {
  key: 'users-paginated',
  query: computed(() => ({ page: page.value, limit: limit.value })),
})

// Change page triggers automatic refetch
const nextPage = () => page.value++
const prevPage = () => page.value--
```

### Search with Debouncing

```typescript
import { debounce } from 'lodash-es'

const search = ref('')
const searchResults = ref([])

const { searchUsers } = useUsers()

const debouncedSearch = debounce(async (term: string) => {
  if (term) {
    searchResults.value = await searchUsers(term)
  }
  else {
    searchResults.value = []
  }
}, 300)

watch(search, (newTerm) => {
  debouncedSearch(newTerm)
})
```

This API client provides a robust foundation for handling all your data fetching needs in Nuxt 3 applications. It follows current best practices and provides excellent developer experience with full TypeScript support.
