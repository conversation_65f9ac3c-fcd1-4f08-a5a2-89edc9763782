# Handling the Shadcn Select Empty Value Error

This document explains how to resolve the common error: `A <SelectItem /> must have a value prop that is not an empty string.`

## The Problem

When using the `Select` component from `shadcn-vue` (which is built on top of Radix UI), you cannot use an empty string (`""`) as the `value` for a `SelectItem`.

This is because the parent `Select` component internally uses an empty string to manage its state, specifically to clear the selection and show the placeholder text. Assigning an empty string to an item creates a conflict.

This issue commonly arises when creating a filter dropdown where one of the options is intended to clear the filter or "show all" items, which is often represented by an empty value.

For more technical details, see the related [Radix UI GitHub issue #3390](httpss://github.com/radix-ui/primitives/issues/3390).

## The Solution

To fix this, you must use a non-empty, descriptive string for the "clear" or "default" option and then handle that value in your application logic.

### Step 1: Update the Item Value

Instead of an empty string, use a meaningful value like `'all'`.

**Before (Causes Error):**
```typescript
const statusOptions = [
  { value: '', label: 'All Orders' }, // Error-prone
  { value: 'pending', label: 'Pending' },
  { value: 'completed', label: 'Completed' },
]
```

**After (Corrected):**
```typescript
const statusOptions = [
  { value: 'all', label: 'All Orders' }, // Correct
  { value: 'pending', label: 'Pending' },
  { value: 'completed', label: 'Completed' },
]
```

### Step 2: Handle the New Value in Your Logic

In the function that handles the selection change (`@update:model-value`), check for your new default value (`'all'`) and convert it to `undefined` or `null` before sending it to an API or updating your state. This ensures that "selecting all" correctly results in no filter being applied.

**Before:**
```typescript
function handleStatusFilter(status: any) {
  setQueryParam({ status: status || undefined })
}
```

**After (Corrected):**
```typescript
function handleStatusFilter(status: any) {
  // Convert "all" to undefined for the API call
  const newStatus = status === 'all' ? undefined : status
  setQueryParam({ status: newStatus })
}
```

### Step 3: Update the Select Component's Bound Value

Finally, ensure the `Select` component's `v-model` or `:model-value` correctly reflects the default state. If the query parameter is `undefined`, the dropdown should show your default option (e.g., "All Orders").

**Before:**
```html
<Select
  :model-value="queryParams.status as string || ''"
  @update:model-value="handleStatusFilter"
>
  ...
</Select>
```

**After (Corrected):**
```html
<Select
  :model-value="(queryParams.status as string) || 'all'"
  @update:model-value="handleStatusFilter"
>
  ...
</Select>
```

By following these steps, you can create a clear and intuitive "show all" option in your `Select` components without conflicting with the underlying library's behavior.
