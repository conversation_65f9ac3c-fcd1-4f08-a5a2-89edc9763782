{"name": "nuxt-app", "type": "module", "private": true, "packageManager": "pnpm@10.13.1+sha1.aa8c167c4509c97519542ef77a09e4b8ab59fb6a", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "1.6.0", "@tailwindcss/vite": "^4.1.11", "@vueuse/core": "^13.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.31.0", "lucide-vue-next": "^0.525.0", "nuxt": "^4.0.0", "reka-ui": "^2.3.2", "shadcn-nuxt": "2.2.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^4.17.0", "eslint": "^9.31.0", "eslint-plugin-format": "^1.0.1", "typescript": "^5.8.3"}}