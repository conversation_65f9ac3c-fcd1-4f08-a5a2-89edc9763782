# Multi-stage Dockerfile for Nuxt 3 Admin Dashboard

# Stage 1: Build stage
FROM node:20-alpine AS builder

# Install pnpm globally
RUN npm install -g pnpm@9.15.1

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json ./

# Install dependencies (will create fresh lockfile)
RUN pnpm install

# Copy source code
COPY . .

# Build the application
RUN pnpm build

# Stage 2: Production stage
FROM node:20-alpine AS production

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder /app/.output ./.output

# Copy package.json for metadata (optional)
COPY package.json ./

# Copy environment file (if exists)
COPY .env* ./

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nuxt -u 1001

# Change ownership of the app directory to nuxt user
RUN chown -R nuxt:nodejs /app
USER nuxt

# Expose port 3005
EXPOSE 3005

# Set environment variables
ENV NODE_ENV=production
ENV HOST=0.0.0.0
ENV PORT=3005

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node --version || exit 1

# Start the application
CMD ["node", ".output/server/index.mjs"]
