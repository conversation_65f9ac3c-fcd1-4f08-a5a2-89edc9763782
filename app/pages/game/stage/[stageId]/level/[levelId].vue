<script setup lang="ts">
import GameOverModal from '@/components/modals/GameOverModal.vue'
import LevelCompleteModal from '@/components/modals/LevelCompleteModal.vue'
import type { ProcessedQuestion, GameState } from '@/types/question'

// Route parameters
const route = useRoute()
const currentStage = ref(Number(route.params.stageId))
const currentLevel = ref(Number(route.params.levelId))

// Modal system
const modal = useModal()

// Questions composable following API client guide pattern
const { fetchQuestionsForLevel, submitQuestionAnswer } = useQuestions()

// Game state
const gameState = ref<GameState>({
  currentQuestion: null,
  questions: [],
  currentQuestionIndex: 0,
  selectedAnswer: null,
  answerSelected: false,
  showFeedback: false,
  isCorrect: false,
  lives: 3,
  maxLives: 3,
  score: 0,
  loading: true,
  error: null
})

// Reactive getters for template
const currentQuestionIndex = computed(() => gameState.value.currentQuestionIndex)
const selectedAnswer = computed(() => gameState.value.selectedAnswer)
const answerSelected = computed(() => gameState.value.answerSelected)
const showFeedback = computed(() => gameState.value.showFeedback)
const isCorrect = computed(() => gameState.value.isCorrect)
const lives = computed(() => gameState.value.lives)
const maxLives = computed(() => gameState.value.maxLives)
const score = computed(() => gameState.value.score)
const currentQuestion = computed(() => gameState.value.currentQuestion)
const questions = computed(() => gameState.value.questions)
const loading = computed(() => gameState.value.loading)
const error = computed(() => gameState.value.error)

const stageLevels = ref([
  { id: 1, completed: false, current: true },
  { id: 2, completed: false, current: false },
  { id: 3, completed: false, current: false },
  { id: 4, completed: false, current: false },
  { id: 5, completed: false, current: false },
])

// Load questions for current level using API client pattern
const { data: levelData, error: fetchError, pending } = await fetchQuestionsForLevel(
  currentStage.value, 
  currentLevel.value
)

// Initialize game state with fetched data
if (levelData.value && !fetchError.value) {
  gameState.value.questions = levelData.value.questions
  gameState.value.currentQuestion = levelData.value.questions[0] || null
  gameState.value.loading = false
} else if (fetchError.value) {
  gameState.value.error = 'Failed to load questions. Please try again.'
  gameState.value.loading = false
}

// Watch for loading state changes
watch(pending, (newPending) => {
  gameState.value.loading = newPending
})

// Computed properties
const totalQuestions = computed(() => gameState.value.questions.length)
const progressPercentage = computed(() =>
  ((gameState.value.currentQuestionIndex + (gameState.value.showFeedback ? 1 : 0)) / totalQuestions.value) * 100,
)
const isLastQuestion = computed(() => gameState.value.currentQuestionIndex >= totalQuestions.value - 1)

// Methods
async function selectAnswer(index: number) {
  if (gameState.value.answerSelected || !gameState.value.currentQuestion)
    return

  const currentQ = gameState.value.currentQuestion
  const isAnswerCorrect = currentQ.options[index]?.isCorrect ?? false

  // Update game state
  gameState.value.selectedAnswer = index
  gameState.value.answerSelected = true
  gameState.value.isCorrect = isAnswerCorrect
  gameState.value.showFeedback = true

  // Submit answer to API for tracking
  try {
    await submitQuestionAnswer(
      currentQ.id,
      index,
      isAnswerCorrect,
      currentStage.value,
      currentLevel.value
    )
  } catch (error) {
    console.warn('Failed to submit answer:', error)
  }

  if (!isAnswerCorrect) {
    gameState.value.lives--
    if (gameState.value.lives <= 0) {
      // Game over logic - show modal instead of alert
      setTimeout(async () => {
        const action = await modal.custom(GameOverModal, {
          score: gameState.value.score,
          correctAnswers: gameState.value.currentQuestionIndex,
          totalQuestions: totalQuestions.value,
          stage: currentStage.value,
          level: currentLevel.value,
        }, {
          title: 'Game Over',
          size: 'md',
          persistent: true,
        })

        if (action === 'tryAgain') {
          // Reset game state
          resetGameState()
        }
        else {
          // Go to main menu
          navigateTo('/')
        }
      }, 1500)
    }
  }
  else {
    gameState.value.score += 10
  }
}

function nextQuestion() {
  if (isLastQuestion.value) {
    // Level completed
    completeLevel()
  }
  else {
    // Next question
    gameState.value.currentQuestionIndex++
    gameState.value.currentQuestion = gameState.value.questions[gameState.value.currentQuestionIndex]
    resetQuestionState()
  }
}

function resetQuestionState() {
  gameState.value.selectedAnswer = null
  gameState.value.answerSelected = false
  gameState.value.showFeedback = false
  gameState.value.isCorrect = false
}

function resetGameState() {
  gameState.value.lives = gameState.value.maxLives
  gameState.value.score = 0
  gameState.value.currentQuestionIndex = 0
  gameState.value.currentQuestion = gameState.value.questions[0] || null
  resetQuestionState()
}

async function completeLevel() {
  const correctAnswers = gameState.value.currentQuestionIndex + 1 // Include current question
  const achievements = []

  // Calculate achievements
  const accuracy = Math.round((correctAnswers / totalQuestions.value) * 100)
  if (accuracy === 100)
    achievements.push('Perfect Score!')
  if (gameState.value.lives === gameState.value.maxLives)
    achievements.push('Flawless Victory!')
  if (gameState.value.score >= 100)
    achievements.push('High Scorer!')

  const action = await modal.custom(LevelCompleteModal, {
    stage: currentStage.value,
    level: currentLevel.value,
    score: gameState.value.score,
    correctAnswers,
    totalQuestions: totalQuestions.value,
    isLastLevel: currentLevel.value >= 5,
    achievements,
  }, {
    title: 'Level Complete!',
    size: 'md',
    persistent: true,
  })

  if (action === 'next') {
    if (currentLevel.value < 5) {
      navigateTo(`/game/stage/${currentStage.value}/level/${currentLevel.value + 1}`)
    }
    else {
      // Complete stage, show celebration
      navigateTo('/celebration')
    }
  }
  else if (action === 'replay') {
    // Reset level
    resetGameState()
  }
}

function goBack() {
  navigateTo('/')
}

function getLevelStatusClass(level: any) {
  if (level.id === currentLevel.value) {
    return 'bg-blue-500 text-white'
  }
  if (level.completed) {
    return 'bg-green-500 text-white'
  }
  return 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
}

// Set page meta
useHead({
  title: `Stage ${currentStage.value} - Level ${currentLevel.value} | English Game`,
  meta: [
    { name: 'description', content: 'Play English learning game questions and improve your language skills.' },
  ],
})

onMounted(() => {
  // Initialize game state only if questions are loaded
  if (gameState.value.questions.length > 0) {
    resetQuestionState()
  }
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900">
    <div class="container mx-auto px-4 py-6">
      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center min-h-96">
        <div class="text-center">
          <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-lg text-muted-foreground">Loading questions...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="flex items-center justify-center min-h-96">
        <div class="text-center max-w-md">
          <div class="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-foreground mb-2">Failed to Load Questions</h3>
          <p class="text-muted-foreground mb-4">{{ error }}</p>
          <Button @click="() => navigateTo('/')" variant="outline">
            Back to Home
          </Button>
        </div>
      </div>

      <!-- Game Content -->
      <template v-else-if="currentQuestion">
        <!-- Game Header -->
        <GameNavigation
          :current-stage="currentStage"
          :current-level="currentLevel"
          :current-question-index="currentQuestionIndex"
          :total-questions="totalQuestions"
          :lives="lives"
          :max-lives="maxLives"
          :score="score"
          :progress-percentage="progressPercentage"
          @go-back="goBack"
        />

        <!-- Game Content -->
        <main class="max-w-4xl mx-auto">
          <!-- Question Card -->
          <QuestionCard
            :question="currentQuestion"
            :selected-answer="selectedAnswer"
            :answer-selected="answerSelected"
            :show-feedback="showFeedback"
            :is-correct="isCorrect"
            :is-last-question="isLastQuestion"
            class="mb-8"
            @select-answer="selectAnswer"
            @next-question="nextQuestion"
          />

          <!-- Stage Progress Overview -->
          <Card>
            <CardHeader>
              <CardTitle>Stage Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="flex gap-2 overflow-x-auto">
                <div
                  v-for="level in stageLevels"
                  :key="level.id"
                  :class="getLevelStatusClass(level)"
                  class="min-w-12 h-12 rounded-lg flex items-center justify-center font-bold text-sm transition-all"
                >
                  {{ level.id }}
                </div>
              </div>
            </CardContent>
          </Card>
        </main>
      </template>

      <!-- No Questions State -->
      <div v-else class="flex items-center justify-center min-h-96">
        <div class="text-center">
          <h3 class="text-xl font-semibold text-foreground mb-2">No Questions Available</h3>
          <p class="text-muted-foreground mb-4">This level doesn't have any questions yet.</p>
          <Button @click="goBack" variant="outline">
            Back to Home
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
