<script setup lang="ts">
import { Award, BarChart3, Clock, HelpCircle, Play, Zap } from 'lucide-vue-next'
import { useModal } from '@/composables/useModal'

// Sample data - in real app this would come from API/store
const hasProgress = ref(false) // This would check for saved game progress

// Modal system
const modal = useModal()

async function startNewGame() {
  // Show confirmation modal before starting
  // const confirmed = await modal.confirm({
  //   title: 'Start New Game',
  //   description: 'Are you ready to begin your English learning adventure?',
  //   variant: 'default'
  // })

  // if (confirmed) {
  // Navigate to game start
  navigateTo('/game/stage/1/level/1')
  // }
}

async function continueGame() {
  if (!hasProgress.value) {
    // Show alert if no progress exists
    await modal.alert({
      title: 'No Progress Found',
      description: 'You haven\'t started any games yet. Please start a new game to begin learning!',
      variant: 'info',
    })
    return
  }

  // Navigate to saved progress
  // This would load from localStorage or API
  navigateTo('/game/stage/1/level/1')
}

function viewStats() {
  // Navigate to statistics page
  navigateTo('/stats')
}

// Set page meta
useHead({
  title: 'English Learning Game - Master English Through Play',
  meta: [
    { name: 'description', content: 'Learn English through interactive gaming. Progress through stages, answer questions, and improve your language skills.' },
  ],
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900">
    <div class="container mx-auto px-4 py-8">
      <!-- Header -->
      <header class="text-center mb-12">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-4">
          🎯 English Learning Game
        </h1>
        <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Master English through interactive gameplay. Progress through stages, answer questions, and unlock your potential!
        </p>
      </header>

      <!-- Game Selection Cards -->
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Start New Game Card -->
        <Card class="hover:shadow-xl transition-all duration-300 cursor-pointer group">
          <CardContent class="p-6">
            <div class="text-center">
              <div class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Play class="w-8 h-8 text-green-600 dark:text-green-400" />
              </div>
              <CardTitle class="mb-2">
                New Game
              </CardTitle>
              <CardDescription class="mb-4">
                Start your English learning journey
              </CardDescription>
              <Button
                class="w-full"
                size="lg"
                @click="startNewGame"
              >
                Start Playing
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- Continue Game Card -->
        <Card class="hover:shadow-xl transition-all duration-300 cursor-pointer group">
          <CardContent class="p-6">
            <div class="text-center">
              <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Clock class="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
              <CardTitle class="mb-2">
                Continue
              </CardTitle>
              <CardDescription class="mb-4">
                Resume your progress
              </CardDescription>
              <Button
                variant="outline"
                class="w-full"
                size="lg"
                :disabled="!hasProgress"
                @click="continueGame"
              >
                Continue Game
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- Statistics Card -->
        <Card class="hover:shadow-xl transition-all duration-300 cursor-pointer group">
          <CardContent class="p-6">
            <div class="text-center">
              <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <BarChart3 class="w-8 h-8 text-purple-600 dark:text-purple-400" />
              </div>
              <CardTitle class="mb-2">
                Statistics
              </CardTitle>
              <CardDescription class="mb-4">
                View your progress
              </CardDescription>
              <Button
                variant="secondary"
                class="w-full"
                size="lg"
                @click="viewStats"
              >
                View Stats
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Game Features Preview -->
      <Card>
        <CardHeader>
          <CardTitle class="text-center">
            Game Features
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid md:grid-cols-3 gap-6">
            <div class="text-center">
              <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Zap class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <h3 class="font-semibold text-card-foreground mb-2">
                Multiple Stages
              </h3>
              <CardDescription>Progress through increasingly challenging levels</CardDescription>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                <HelpCircle class="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <h3 class="font-semibold text-card-foreground mb-2">
                Interactive Questions
              </h3>
              <CardDescription>Answer multiple choice questions to advance</CardDescription>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Award class="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 class="font-semibold text-card-foreground mb-2">
                Achievement System
              </h3>
              <CardDescription>Earn rewards and celebrate victories</CardDescription>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
