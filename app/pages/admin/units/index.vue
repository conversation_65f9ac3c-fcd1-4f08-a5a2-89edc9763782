<script setup lang="ts">
import type { Difficulty, SkillType, Unit } from '@/types/course'
import { BookOpen, Copy, Edit, Filter, MoreHorizontal, Plus, Search, Trash2 } from 'lucide-vue-next'
import { computed, ref, watch } from 'vue'
import { courseRepository } from '@/repositories/course'
import { unitRepository } from '@/repositories/unit'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Unit Management',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Units', active: true },
  ],
  showAuthStatus: true,
})

// Query params and search
const { queryParams, setQueryParam } = useQueryParams()
const { search, nextPage, prevPage, goToPage } = useBaseQuery()

// Local search state for debouncing
const searchQuery = ref((queryParams.value.q as string) || '')

// Filter options
const skillTypeOptions = [
  { value: 'all', label: 'All Skill Types' },
  { value: 'vocabulary', label: 'Vocabulary' },
  { value: 'grammar', label: 'Grammar' },
  { value: 'listening', label: 'Listening' },
  { value: 'reading', label: 'Reading' },
  { value: 'speaking', label: 'Speaking' },
  { value: 'writing', label: 'Writing' },
]

const difficultyOptions = [
  { value: 'all', label: 'All Difficulties' },
  { value: 'beginner', label: 'Beginner' },
  { value: 'elementary', label: 'Elementary' },
  { value: 'intermediate', label: 'Intermediate' },
  { value: 'upper_intermediate', label: 'Upper Intermediate' },
  { value: 'advanced', label: 'Advanced' },
  { value: 'proficient', label: 'Proficient' },
]

// Watch for search input changes with debouncing
watch(searchQuery, (newValue) => {
  const timeoutId = setTimeout(() => {
    search(newValue)
  }, 300)

  return () => clearTimeout(timeoutId)
}, { immediate: false })

// Data fetching
const { data, pending: loading, error, refresh: fetchUnits } = useAsyncData(
  'unitsList',
  () => {
    const params: any = { per_page: 15 }

    if (queryParams.value.course_id) {
      params.course_id = Number(queryParams.value.course_id)
    }
    if (queryParams.value.skill_type && queryParams.value.skill_type !== 'all') {
      params.skill_type = queryParams.value.skill_type as SkillType
    }
    if (queryParams.value.difficulty && queryParams.value.difficulty !== 'all') {
      params.difficulty = queryParams.value.difficulty as Difficulty
    }

    return unitRepository.listUnits(params)
  },
  {
    watch: [queryParams],
  },
)

// Fetch courses for filter
const { data: coursesData } = useAsyncData(
  'coursesForFilter',
  () => courseRepository.listCourses({ per_page: 100 }),
)

// Computed properties
const units = computed(() => data.value?.data || [])
const pagination = computed(() => data.value)
const courses = computed(() => coursesData.value?.data || [])

// Filter handlers
function handleSkillTypeFilter(skillType: string) {
  const newSkillType = skillType === 'all' ? undefined : skillType
  setQueryParam({ skill_type: newSkillType, page: undefined })
}

function handleDifficultyFilter(difficulty: string) {
  const newDifficulty = difficulty === 'all' ? undefined : difficulty
  setQueryParam({ difficulty: newDifficulty, page: undefined })
}

function handleCourseFilter(courseId: string) {
  const newCourseId = courseId === 'all' ? undefined : courseId
  setQueryParam({ course_id: newCourseId, page: undefined })
}

// Actions
const { open: openModal } = useModal()

async function handleDeleteUnit(unit: Unit) {
  const confirmed = await openModal({
    title: 'Delete Unit',
    description: `Are you sure you want to delete "${unit.title}"? This action cannot be undone.`,
    variant: 'destructive',
    buttons: [
      { text: 'Cancel', variant: 'outline' },
      { text: 'Delete', variant: 'destructive' },
    ],
  })

  if (confirmed) {
    try {
      await unitRepository.deleteUnit(unit.id)
      await fetchUnits()
      // Show success toast
    }
    catch (error: any) {
      console.error('Failed to delete unit:', error)
      // Show error toast
    }
  }
}

async function handleDuplicateUnit(unit: Unit) {
  try {
    await unitRepository.duplicateUnit(unit.id, { target_order: unit.unit_order + 1 })
    await fetchUnits()
    // Show success toast
  }
  catch (error: any) {
    console.error('Failed to duplicate unit:', error)
    // Show error toast
  }
}

// Page meta
useHead({
  title: 'Unit Management | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Manage learning units for the English Learning Game. Create, edit, and organize course content.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Unit Management
        </h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Create and manage learning units
        </p>
      </div>
      <Button as-child>
        <NuxtLink to="/admin/units/create">
          <Plus class="w-4 h-4 mr-2" />
          Create Unit
        </NuxtLink>
      </Button>
    </div>

    <!-- Search and Filters -->
    <Card>
      <CardContent class="p-6">
        <div class="flex flex-col lg:flex-row items-start lg:items-center gap-4">
          <!-- Search -->
          <div class="relative flex-1">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              v-model="searchQuery"
              placeholder="Search units..."
              class="pl-10"
            />
          </div>

          <!-- Filters -->
          <div class="flex flex-wrap items-center gap-4">
            <!-- Course Filter -->
            <div class="min-w-[180px]">
              <Select
                :model-value="(queryParams.course_id as string) || 'all'"
                @update:model-value="handleCourseFilter"
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Courses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    All Courses
                  </SelectItem>
                  <SelectItem
                    v-for="course in courses"
                    :key="course.id"
                    :value="course.id.toString()"
                  >
                    {{ course.title }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Skill Type Filter -->
            <div class="min-w-[160px]">
              <Select
                :model-value="(queryParams.skill_type as string) || 'all'"
                @update:model-value="handleSkillTypeFilter"
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Skills" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in skillTypeOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Difficulty Filter -->
            <div class="min-w-[160px]">
              <Select
                :model-value="(queryParams.difficulty as string) || 'all'"
                @update:model-value="handleDifficultyFilter"
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in difficultyOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Units Table -->
    <Card>
      <CardHeader>
        <CardTitle>Units</CardTitle>
        <CardDescription>
          {{ pagination?.total || 0 }} units total
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div v-if="loading" class="flex items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        </div>

        <div v-else-if="error" class="text-center py-8">
          <p class="text-red-600 dark:text-red-400">
            Failed to load units
          </p>
          <Button variant="outline" class="mt-2" @click="fetchUnits">
            Try Again
          </Button>
        </div>

        <div v-else-if="units.length === 0" class="text-center py-8">
          <BookOpen class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-600 dark:text-gray-300">
            No units found
          </p>
          <Button as-child class="mt-4">
            <NuxtLink to="/admin/units/create">
              Create Your First Unit
            </NuxtLink>
          </Button>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="unit in units"
            :key="unit.id"
            class="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <h3 class="font-semibold text-gray-900 dark:text-white">
                  {{ unit.title }}
                </h3>
                <Badge variant="secondary">
                  {{ unit.skill_type }}
                </Badge>
                <Badge variant="outline">
                  {{ unit.difficulty }}
                </Badge>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">
                {{ unit.description }}
              </p>
              <div class="flex items-center gap-4 text-xs text-gray-500">
                <span>{{ unit.course?.title }}</span>
                <span>Order: {{ unit.unit_order }}</span>
                <span>{{ unit.assessments?.length || 0 }} assessments</span>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal class="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem as-child>
                  <NuxtLink :to="`/admin/units/${unit.id}`">
                    <Edit class="w-4 h-4 mr-2" />
                    Edit
                  </NuxtLink>
                </DropdownMenuItem>
                <DropdownMenuItem @click="handleDuplicateUnit(unit)">
                  <Copy class="w-4 h-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  class="text-red-600 focus:text-red-600"
                  @click="handleDeleteUnit(unit)"
                >
                  <Trash2 class="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Pagination -->
    <div v-if="pagination && pagination.total > pagination.per_page" class="flex items-center justify-between">
      <p class="text-sm text-gray-600 dark:text-gray-300">
        Showing {{ pagination.from }} to {{ pagination.to }} of {{ pagination.total }} units
      </p>

      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.current_page <= 1"
          @click="prevPage"
        >
          Previous
        </Button>

        <span class="text-sm text-gray-600 dark:text-gray-300">
          Page {{ pagination.current_page }} of {{ pagination.last_page }}
        </span>

        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.current_page >= pagination.last_page"
          @click="nextPage"
        >
          Next
        </Button>
      </div>
    </div>
  </div>
</template>
