<script setup lang="ts">
import { ref, computed } from 'vue'
import { ArrowLeft, Save } from 'lucide-vue-next'
import { unitRepository } from '@/repositories/unit'
import { courseRepository } from '@/repositories/course'
import type { CreateUnitData, SkillType, Difficulty } from '@/types/course'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Create Unit',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Units', href: '/admin/units' },
    { label: 'Create Unit', active: true },
  ],
  showAuthStatus: true,
})

// Route and query params
const route = useRoute()
const router = useRouter()

// Form state
const form = ref<CreateUnitData>({
  course_id: Number(route.query.course_id) || 0,
  title: '',
  description: '',
  skill_type: 'vocabulary' as SkillType,
  difficulty: 'beginner' as Difficulty,
  unit_order: 1,
})

const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Fetch courses for selection
const { data: coursesData } = useAsyncData(
  'coursesForUnitCreate',
  () => courseRepository.listCourses({ per_page: 100 }),
)

const courses = computed(() => coursesData.value?.data || [])

// Options
const skillTypeOptions = [
  { value: 'vocabulary', label: 'Vocabulary' },
  { value: 'grammar', label: 'Grammar' },
  { value: 'listening', label: 'Listening' },
  { value: 'reading', label: 'Reading' },
  { value: 'speaking', label: 'Speaking' },
  { value: 'writing', label: 'Writing' },
]

const difficultyOptions = [
  { value: 'beginner', label: 'Beginner' },
  { value: 'elementary', label: 'Elementary' },
  { value: 'intermediate', label: 'Intermediate' },
  { value: 'upper_intermediate', label: 'Upper Intermediate' },
  { value: 'advanced', label: 'Advanced' },
  { value: 'proficient', label: 'Proficient' },
]

// Form validation
function validateForm(): boolean {
  errors.value = {}
  
  if (!form.value.course_id) {
    errors.value.course_id = ['Please select a course']
  }
  
  if (!form.value.title.trim()) {
    errors.value.title = ['Unit title is required']
  }
  
  if (!form.value.description.trim()) {
    errors.value.description = ['Unit description is required']
  }
  
  if (form.value.unit_order < 1) {
    errors.value.unit_order = ['Unit order must be at least 1']
  }
  
  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }
  
  loading.value = true
  
  try {
    const unit = await unitRepository.createUnit(form.value)
    
    // Navigate to the unit edit page
    await router.push(`/admin/units/${unit.id}`)
    
    // Show success toast
    console.log('Unit created successfully:', unit)
  } catch (error: any) {
    console.error('Failed to create unit:', error)
    
    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
    } else {
      // Show generic error toast
      errors.value.general = ['Failed to create unit. Please try again.']
    }
  } finally {
    loading.value = false
  }
}

// Page meta
useHead({
  title: 'Create Unit | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Create a new learning unit for the English Learning Game.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div class="flex items-center gap-4">
      <Button variant="ghost" size="sm" as-child>
        <NuxtLink to="/admin/units">
          <ArrowLeft class="w-4 h-4 mr-2" />
          Back to Units
        </NuxtLink>
      </Button>
      
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Create Unit
        </h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Add a new learning unit to a course
        </p>
      </div>
    </div>

    <!-- Form -->
    <div class="max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Unit Information</CardTitle>
          <CardDescription>
            Provide the details for your new learning unit
          </CardDescription>
        </CardHeader>
        
        <CardContent class="space-y-6">
          <form @submit.prevent="handleSubmit">
            <!-- General Error -->
            <div v-if="errors.general" class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="text-red-800">
                <ul class="list-disc list-inside space-y-1">
                  <li v-for="error in errors.general" :key="error">{{ error }}</li>
                </ul>
              </div>
            </div>

            <!-- Course Selection -->
            <div class="space-y-2">
              <Label for="course">Course *</Label>
              <Select v-model="form.course_id">
                <SelectTrigger :class="{ 'border-red-500': errors.course_id }">
                  <SelectValue placeholder="Select a course" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="course in courses"
                    :key="course.id"
                    :value="course.id"
                  >
                    {{ course.title }}
                  </SelectItem>
                </SelectContent>
              </Select>
              <div v-if="errors.course_id" class="text-red-600 text-sm">
                <ul class="list-disc list-inside">
                  <li v-for="error in errors.course_id" :key="error">{{ error }}</li>
                </ul>
              </div>
            </div>

            <!-- Unit Title -->
            <div class="space-y-2">
              <Label for="title">Unit Title *</Label>
              <Input
                id="title"
                v-model="form.title"
                placeholder="Enter unit title"
                :class="{ 'border-red-500': errors.title }"
                required
              />
              <div v-if="errors.title" class="text-red-600 text-sm">
                <ul class="list-disc list-inside">
                  <li v-for="error in errors.title" :key="error">{{ error }}</li>
                </ul>
              </div>
            </div>

            <!-- Unit Description -->
            <div class="space-y-2">
              <Label for="description">Unit Description *</Label>
              <Textarea
                id="description"
                v-model="form.description"
                placeholder="Enter unit description"
                rows="3"
                :class="{ 'border-red-500': errors.description }"
                required
              />
              <div v-if="errors.description" class="text-red-600 text-sm">
                <ul class="list-disc list-inside">
                  <li v-for="error in errors.description" :key="error">{{ error }}</li>
                </ul>
              </div>
            </div>

            <!-- Skill Type and Difficulty -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Skill Type -->
              <div class="space-y-2">
                <Label for="skill_type">Skill Type *</Label>
                <Select v-model="form.skill_type">
                  <SelectTrigger>
                    <SelectValue placeholder="Select skill type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="option in skillTypeOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <!-- Difficulty -->
              <div class="space-y-2">
                <Label for="difficulty">Difficulty *</Label>
                <Select v-model="form.difficulty">
                  <SelectTrigger>
                    <SelectValue placeholder="Select difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="option in difficultyOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <!-- Unit Order -->
            <div class="space-y-2">
              <Label for="unit_order">Unit Order *</Label>
              <Input
                id="unit_order"
                v-model.number="form.unit_order"
                type="number"
                min="1"
                placeholder="Enter unit order"
                :class="{ 'border-red-500': errors.unit_order }"
                required
              />
              <div v-if="errors.unit_order" class="text-red-600 text-sm">
                <ul class="list-disc list-inside">
                  <li v-for="error in errors.unit_order" :key="error">{{ error }}</li>
                </ul>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center gap-4 pt-6">
              <Button
                type="submit"
                :disabled="loading"
                class="min-w-[120px]"
              >
                <div v-if="loading" class="flex items-center gap-2">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                  <span>Creating...</span>
                </div>
                <div v-else class="flex items-center gap-2">
                  <Save class="w-4 h-4" />
                  <span>Create Unit</span>
                </div>
              </Button>
              
              <Button variant="outline" as-child>
                <NuxtLink to="/admin/units">
                  Cancel
                </NuxtLink>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
