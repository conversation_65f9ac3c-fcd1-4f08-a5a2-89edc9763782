<script setup lang="ts">
import type { UpdateCourseData } from '@/types/course'
import { ArrowLeft, Plus, Save } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { courseRepository } from '@/repositories/course'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Edit Course',
  middleware: 'auth',
})

// Route params
const route = useRoute()
const courseId = computed(() => Number(route.params.id))

// Fetch course data
const { data: course, pending: loadingCourse, error: courseError, refresh: fetchCourse } = useAsyncData(
  `course-${courseId.value}`,
  () => courseRepository.getCourse(courseId.value),
)

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Courses', href: '/admin/courses' },
    { label: course.value?.title || 'Edit Course', active: true },
  ],
  showAuthStatus: true,
})

// Form state
const form = ref<UpdateCourseData>({
  title: '',
  description: '',
})

// Initialize form when course data loads
watch(course, (newCourse) => {
  if (newCourse) {
    form.value = {
      title: newCourse.title,
      description: newCourse.description,
    }
  }
}, { immediate: true })

const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Router
const router = useRouter()

// Form validation
function validateForm(): boolean {
  errors.value = {}

  if (!form.value.title?.trim()) {
    errors.value.title = ['Course title is required']
  }

  if (!form.value.description?.trim()) {
    errors.value.description = ['Course description is required']
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    await courseRepository.updateCourse(courseId.value, form.value)

    // Refresh course data
    await fetchCourse()

    // Show success toast
    console.log('Course updated successfully')
  }
  catch (error: any) {
    console.error('Failed to update course:', error)

    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
    }
    else {
      // Show generic error toast
      errors.value.general = ['Failed to update course. Please try again.']
    }
  }
  finally {
    loading.value = false
  }
}

// Page meta
useHead({
  title: computed(() => `Edit ${course.value?.title || 'Course'} | English Learning Game Admin`),
  meta: [
    { name: 'description', content: 'Edit course details for the English Learning Game.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Loading State -->
    <div v-if="loadingCourse" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
    </div>

    <!-- Error State -->
    <div v-else-if="courseError" class="text-center py-12">
      <p class="text-red-600 dark:text-red-400 mb-4">
        Failed to load course
      </p>
      <Button variant="outline" @click="fetchCourse">
        Try Again
      </Button>
    </div>

    <!-- Course Edit Form -->
    <template v-else-if="course">
      <!-- Page Header -->
      <div class="flex items-center gap-4">
        <Button variant="ghost" size="sm" as-child>
          <NuxtLink to="/admin/courses">
            <ArrowLeft class="w-4 h-4 mr-2" />
            Back to Courses
          </NuxtLink>
        </Button>

        <div class="flex-1">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Edit Course
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Update course information and manage units
          </p>
        </div>

        <Button as-child>
          <NuxtLink :to="`/admin/units/create?course_id=${courseId}`">
            <Plus class="w-4 h-4 mr-2" />
            Add Unit
          </NuxtLink>
        </Button>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Course Information Form -->
        <div class="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Course Information</CardTitle>
              <CardDescription>
                Update the basic information for this course
              </CardDescription>
            </CardHeader>

            <CardContent class="space-y-6">
              <form class="space-y-6" @submit.prevent="handleSubmit">
                <!-- General Error -->
                <div v-if="errors.general" class="bg-red-50 border border-red-200 rounded-md p-4">
                  <div class="text-red-800">
                    <ul class="list-disc list-inside space-y-1">
                      <li v-for="error in errors.general" :key="error">
                        {{ error }}
                      </li>
                    </ul>
                  </div>
                </div>

                <!-- Course Title -->
                <div class="space-y-2">
                  <Label for="title">Course Title *</Label>
                  <Input
                    id="title"
                    v-model="form.title"
                    placeholder="Enter course title"
                    :class="{ 'border-red-500': errors.title }"
                    required
                  />
                  <div v-if="errors.title" class="text-red-600 text-sm">
                    <ul class="list-disc list-inside">
                      <li v-for="error in errors.title" :key="error">
                        {{ error }}
                      </li>
                    </ul>
                  </div>
                </div>

                <!-- Course Description -->
                <div class="space-y-2">
                  <Label for="description">Course Description *</Label>
                  <Textarea
                    id="description"
                    v-model="form.description"
                    placeholder="Enter course description"
                    rows="4"
                    :class="{ 'border-red-500': errors.description }"
                    required
                  />
                  <div v-if="errors.description" class="text-red-600 text-sm">
                    <ul class="list-disc list-inside">
                      <li v-for="error in errors.description" :key="error">
                        {{ error }}
                      </li>
                    </ul>
                  </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center gap-4 pt-6">
                  <Button
                    type="submit"
                    :disabled="loading"
                    class="min-w-[120px]"
                  >
                    <div v-if="loading" class="flex items-center gap-2">
                      <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                      <span>Saving...</span>
                    </div>
                    <div v-else class="flex items-center gap-2">
                      <Save class="w-4 h-4" />
                      <span>Save Changes</span>
                    </div>
                  </Button>

                  <Button variant="outline" as-child>
                    <NuxtLink to="/admin/courses">
                      Cancel
                    </NuxtLink>
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        <!-- Course Units -->
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Course Units</CardTitle>
              <CardDescription>
                {{ course.units?.length || 0 }} units in this course
              </CardDescription>
            </CardHeader>

            <CardContent>
              <div v-if="!course.units || course.units.length === 0" class="text-center py-6">
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                  No units yet
                </p>
                <Button size="sm" as-child>
                  <NuxtLink :to="`/admin/units/create?course_id=${courseId}`">
                    <Plus class="w-4 h-4 mr-2" />
                    Add First Unit
                  </NuxtLink>
                </Button>
              </div>

              <div v-else class="space-y-3">
                <div
                  v-for="unit in course.units"
                  :key="unit.id"
                  class="p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <h4 class="font-medium text-gray-900 dark:text-white">
                    {{ unit.title }}
                  </h4>
                  <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ unit.skill_type }} • {{ unit.difficulty }}
                  </p>
                </div>

                <Button size="sm" variant="outline" as-child class="w-full">
                  <NuxtLink :to="`/admin/units?course_id=${courseId}`">
                    View All Units
                  </NuxtLink>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </template>
  </div>
</template>
