<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { BookOpen, Plus, Search, MoreHorizontal, Edit, Trash2, Copy } from 'lucide-vue-next'
import { courseRepository } from '@/repositories/course'
import type { Course } from '@/types/course'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Course Management',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Courses', active: true },
  ],
  showAuthStatus: true,
})

// Query params and search
const { queryParams } = useQueryParams()
const { search, nextPage, prevPage, goToPage } = useBaseQuery()

// Local search state for debouncing
const searchQuery = ref((queryParams.value.q as string) || '')

// Watch for search input changes with debouncing
watch(searchQuery, (newValue) => {
  const timeoutId = setTimeout(() => {
    search(newValue)
  }, 300)
  
  return () => clearTimeout(timeoutId)
}, { immediate: false })

// Data fetching
const { data, pending: loading, error, refresh: fetchCourses } = useAsyncData(
  'coursesList',
  () => courseRepository.listCourses({
    per_page: 15,
  }),
  {
    watch: [queryParams],
  },
)

// Computed properties
const courses = computed(() => data.value?.data || [])
const pagination = computed(() => data.value)

// Actions
const { call } = useAPICall()
const { open: openModal } = useModal()

async function handleDeleteCourse(course: Course) {
  const confirmed = await openModal({
    title: 'Delete Course',
    description: `Are you sure you want to delete "${course.title}"? This action cannot be undone.`,
    variant: 'destructive',
    buttons: [
      { text: 'Cancel', variant: 'outline' },
      { text: 'Delete', variant: 'destructive' },
    ],
  })

  if (confirmed) {
    try {
      await courseRepository.deleteCourse(course.id)
      await fetchCourses()
      // Show success toast
    } catch (error: any) {
      console.error('Failed to delete course:', error)
      // Show error toast
    }
  }
}

async function handleDuplicateCourse(course: Course) {
  try {
    await courseRepository.duplicateCourse(course.id)
    await fetchCourses()
    // Show success toast
  } catch (error: any) {
    console.error('Failed to duplicate course:', error)
    // Show error toast
  }
}

// Page meta
useHead({
  title: 'Course Management | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Manage courses for the English Learning Game. Create, edit, and organize learning content.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Course Management
        </h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Create and manage learning courses
        </p>
      </div>
      <Button as-child>
        <NuxtLink to="/admin/courses/create">
          <Plus class="w-4 h-4 mr-2" />
          Create Course
        </NuxtLink>
      </Button>
    </div>

    <!-- Search and Filters -->
    <Card>
      <CardContent class="p-6">
        <div class="flex items-center gap-4">
          <div class="relative flex-1">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              v-model="searchQuery"
              placeholder="Search courses..."
              class="pl-10"
            />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Courses Table -->
    <Card>
      <CardHeader>
        <CardTitle>Courses</CardTitle>
        <CardDescription>
          {{ pagination?.total || 0 }} courses total
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div v-if="loading" class="flex items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        </div>
        
        <div v-else-if="error" class="text-center py-8">
          <p class="text-red-600 dark:text-red-400">Failed to load courses</p>
          <Button variant="outline" @click="fetchCourses" class="mt-2">
            Try Again
          </Button>
        </div>

        <div v-else-if="courses.length === 0" class="text-center py-8">
          <BookOpen class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-600 dark:text-gray-300">No courses found</p>
          <Button as-child class="mt-4">
            <NuxtLink to="/admin/courses/create">
              Create Your First Course
            </NuxtLink>
          </Button>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="course in courses"
            :key="course.id"
            class="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <div class="flex-1">
              <h3 class="font-semibold text-gray-900 dark:text-white">
                {{ course.title }}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                {{ course.description }}
              </p>
              <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                <span>{{ course.units?.length || 0 }} units</span>
                <span>Created {{ new Date(course.created_at).toLocaleDateString() }}</span>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal class="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem as-child>
                  <NuxtLink :to="`/admin/courses/${course.id}`">
                    <Edit class="w-4 h-4 mr-2" />
                    Edit
                  </NuxtLink>
                </DropdownMenuItem>
                <DropdownMenuItem @click="handleDuplicateCourse(course)">
                  <Copy class="w-4 h-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  @click="handleDeleteCourse(course)"
                  class="text-red-600 focus:text-red-600"
                >
                  <Trash2 class="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Pagination -->
    <div v-if="pagination && pagination.total > pagination.per_page" class="flex items-center justify-between">
      <p class="text-sm text-gray-600 dark:text-gray-300">
        Showing {{ pagination.from }} to {{ pagination.to }} of {{ pagination.total }} courses
      </p>
      
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.current_page <= 1"
          @click="prevPage"
        >
          Previous
        </Button>
        
        <span class="text-sm text-gray-600 dark:text-gray-300">
          Page {{ pagination.current_page }} of {{ pagination.last_page }}
        </span>
        
        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.current_page >= pagination.last_page"
          @click="nextPage"
        >
          Next
        </Button>
      </div>
    </div>
  </div>
</template>
