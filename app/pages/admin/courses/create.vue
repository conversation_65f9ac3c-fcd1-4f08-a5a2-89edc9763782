<script setup lang="ts">
import { ref } from 'vue'
import { ArrowLeft, Save } from 'lucide-vue-next'
import { courseRepository } from '@/repositories/course'
import type { CreateCourseData } from '@/types/course'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Create Course',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Courses', href: '/admin/courses' },
    { label: 'Create Course', active: true },
  ],
  showAuthStatus: true,
})

// Form state
const form = ref<CreateCourseData>({
  title: '',
  description: '',
})

const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Router
const router = useRouter()

// Form validation
function validateForm(): boolean {
  errors.value = {}
  
  if (!form.value.title.trim()) {
    errors.value.title = ['Course title is required']
  }
  
  if (!form.value.description.trim()) {
    errors.value.description = ['Course description is required']
  }
  
  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }
  
  loading.value = true
  
  try {
    const course = await courseRepository.createCourse(form.value)
    
    // Navigate to the course edit page
    await router.push(`/admin/courses/${course.id}`)
    
    // Show success toast
    console.log('Course created successfully:', course)
  } catch (error: any) {
    console.error('Failed to create course:', error)
    
    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
    } else {
      // Show generic error toast
      errors.value.general = ['Failed to create course. Please try again.']
    }
  } finally {
    loading.value = false
  }
}

// Page meta
useHead({
  title: 'Create Course | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Create a new course for the English Learning Game.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div class="flex items-center gap-4">
      <Button variant="ghost" size="sm" as-child>
        <NuxtLink to="/admin/courses">
          <ArrowLeft class="w-4 h-4 mr-2" />
          Back to Courses
        </NuxtLink>
      </Button>
      
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Create Course
        </h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Add a new learning course to the system
        </p>
      </div>
    </div>

    <!-- Form -->
    <div class="max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Course Information</CardTitle>
          <CardDescription>
            Provide the basic information for your new course
          </CardDescription>
        </CardHeader>
        
        <CardContent class="space-y-6">
          <form @submit.prevent="handleSubmit">
            <!-- General Error -->
            <div v-if="errors.general" class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="text-red-800">
                <ul class="list-disc list-inside space-y-1">
                  <li v-for="error in errors.general" :key="error">{{ error }}</li>
                </ul>
              </div>
            </div>

            <!-- Course Title -->
            <div class="space-y-2">
              <Label for="title">Course Title *</Label>
              <Input
                id="title"
                v-model="form.title"
                placeholder="Enter course title"
                :class="{ 'border-red-500': errors.title }"
                required
              />
              <div v-if="errors.title" class="text-red-600 text-sm">
                <ul class="list-disc list-inside">
                  <li v-for="error in errors.title" :key="error">{{ error }}</li>
                </ul>
              </div>
            </div>

            <!-- Course Description -->
            <div class="space-y-2">
              <Label for="description">Course Description *</Label>
              <Textarea
                id="description"
                v-model="form.description"
                placeholder="Enter course description"
                rows="4"
                :class="{ 'border-red-500': errors.description }"
                required
              />
              <div v-if="errors.description" class="text-red-600 text-sm">
                <ul class="list-disc list-inside">
                  <li v-for="error in errors.description" :key="error">{{ error }}</li>
                </ul>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center gap-4 pt-6">
              <Button
                type="submit"
                :disabled="loading"
                class="min-w-[120px]"
              >
                <div v-if="loading" class="flex items-center gap-2">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                  <span>Creating...</span>
                </div>
                <div v-else class="flex items-center gap-2">
                  <Save class="w-4 h-4" />
                  <span>Create Course</span>
                </div>
              </Button>
              
              <Button variant="outline" as-child>
                <NuxtLink to="/admin/courses">
                  Cancel
                </NuxtLink>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
