<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { HelpCircle, MoreHorizontal, Edit, Trash2, Link, CheckCircle } from 'lucide-vue-next'
import { assessmentRepository } from '@/repositories/assessment'
import type { Assessment } from '@/types/course'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Assessment Management',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Assessments', active: true },
  ],
  showAuthStatus: true,
})

// Query params and search
const { queryParams } = useQueryParams()
const { search, nextPage, prevPage, goToPage } = useBaseQuery()

// Local search state for debouncing
const searchQuery = ref((queryParams.value.q as string) || '')

// Watch for search input changes with debouncing
watch(searchQuery, (newValue) => {
  const timeoutId = setTimeout(() => {
    search(newValue)
  }, 300)

  return () => clearTimeout(timeoutId)
}, { immediate: false })

// Data fetching
const { data, pending: loading, error, refresh: fetchAssessments } = useAsyncData(
  'assessmentsList',
  () => assessmentRepository.listAssessments({
    per_page: 15,
  }),
  {
    watch: [queryParams],
  },
)

// Computed properties
const assessments = computed(() => data.value?.data || [])
const pagination = computed(() => data.value)

// Helper functions
function getCorrectAnswers(assessment: Assessment): string[] {
  return assessment.correct_answer_indexes.map(index => assessment.answer_list[index])
}

function getAttachedUnitsCount(assessment: Assessment): number {
  return assessment.assessment?.units?.length || 0
}

// Actions
const { open: openModal } = useModal()

async function handleDeleteAssessment(assessment: Assessment) {
  const confirmed = await openModal({
    title: 'Delete Assessment',
    description: `Are you sure you want to delete this assessment? This action cannot be undone.`,
    variant: 'destructive',
    buttons: [
      { text: 'Cancel', variant: 'outline' },
      { text: 'Delete', variant: 'destructive' },
    ],
  })

  if (confirmed) {
    try {
      await assessmentRepository.deleteAssessment(assessment.id)
      await fetchAssessments()
      // Show success toast
    } catch (error: any) {
      console.error('Failed to delete assessment:', error)
      // Show error toast
    }
  }
}

// Page meta
useHead({
  title: 'Assessment Management | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Manage assessments for the English Learning Game. Create, edit, and organize quiz questions.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <AdminPageHeader
      title="Assessment Management"
      description="Create and manage quiz questions and assessments"
      action-label="Create Assessment"
      action-to="/admin/assessments/create"
    />

    <!-- Search -->
    <AdminSearchBar
      v-model="searchQuery"
      placeholder="Search assessments..."
      :loading="loading"
    />

    <!-- Assessments List -->
    <AdminCard
      title="Assessments"
      :description="`${pagination?.total || 0} assessments total`"
      show-header
    >
      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-6">
          <p class="text-red-700 dark:text-red-400 font-medium mb-3">
            Failed to load assessments
          </p>
          <Button variant="outline" @click="fetchAssessments" class="border-red-300 text-red-700 hover:bg-red-50">
            Try Again
          </Button>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="assessments.length === 0" class="text-center py-12">
        <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-8">
          <HelpCircle class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-700 dark:text-gray-300 font-medium mb-4">
            No assessments found
          </p>
          <Button as-child class="bg-blue-600 hover:bg-blue-700 text-white">
            <NuxtLink to="/admin/assessments/create">
              Create Your First Assessment
            </NuxtLink>
          </Button>
        </div>
      </div>

      <!-- Assessments List -->
      <div v-else class="space-y-4">
        <div
          v-for="assessment in assessments"
          :key="assessment.id"
          class="group bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:bg-white dark:hover:bg-gray-800 hover:shadow-md transition-all duration-200"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <!-- Question -->
              <h3 class="font-semibold text-gray-900 dark:text-white text-lg mb-4 leading-relaxed">
                {{ assessment.question }}
              </h3>

              <!-- Answer Options -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                <div
                  v-for="(option, index) in assessment.answer_list"
                  :key="index"
                  class="flex items-center gap-3 p-3 rounded-lg border transition-colors"
                  :class="{
                    'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700': assessment.correct_answer_indexes.includes(index),
                    'bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-600': !assessment.correct_answer_indexes.includes(index)
                  }"
                >
                  <span class="text-sm font-semibold text-gray-600 dark:text-gray-400 min-w-[20px]">
                    {{ String.fromCharCode(65 + index) }}.
                  </span>
                  <span class="text-sm text-gray-800 dark:text-gray-200 flex-1">{{ option }}</span>
                  <CheckCircle
                    v-if="assessment.correct_answer_indexes.includes(index)"
                    class="w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0"
                  />
                </div>
              </div>

              <!-- Explanation -->
              <div v-if="assessment.explanations?.length" class="mb-4">
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
                  <p class="text-sm text-blue-800 dark:text-blue-200">
                    <span class="font-semibold">Explanation:</span>
                    {{ assessment.explanations.join(', ') }}
                  </p>
                </div>
              </div>

              <!-- Metadata -->
              <div class="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
                <span class="flex items-center gap-1">
                  <span class="font-medium">Units:</span>
                  {{ getAttachedUnitsCount(assessment) }}
                </span>
                <span v-if="assessment.created_at" class="flex items-center gap-1">
                  <span class="font-medium">Created:</span>
                  {{ new Date(assessment.created_at).toLocaleDateString() }}
                </span>
              </div>
            </div>

            <!-- Actions -->
            <div class="ml-6">
              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <Button variant="ghost" size="sm" class="opacity-0 group-hover:opacity-100 transition-opacity">
                    <MoreHorizontal class="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" class="w-48">
                  <DropdownMenuItem as-child>
                    <NuxtLink :to="`/admin/assessments/${assessment.id}`" class="flex items-center">
                      <Edit class="w-4 h-4 mr-2" />
                      Edit Assessment
                    </NuxtLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem as-child>
                    <NuxtLink :to="`/admin/assessments/${assessment.id}/units`" class="flex items-center">
                      <Link class="w-4 h-4 mr-2" />
                      Manage Units
                    </NuxtLink>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    @click="handleDeleteAssessment(assessment)"
                    class="text-red-600 focus:text-red-600 focus:bg-red-50 dark:focus:bg-red-900/20"
                  >
                    <Trash2 class="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    </AdminCard>

    <!-- Pagination -->
    <AdminPagination
      v-if="pagination"
      :pagination="pagination"
      item-name="assessments"
      @prev-page="prevPage"
      @next-page="nextPage"
    />
  </div>
</template>
