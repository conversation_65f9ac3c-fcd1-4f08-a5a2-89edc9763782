<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { HelpCircle, Plus, Search, MoreHorizontal, Edit, Trash2, Link, CheckCircle } from 'lucide-vue-next'
import { assessmentRepository } from '@/repositories/assessment'
import type { Assessment } from '@/types/course'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Assessment Management',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Assessments', active: true },
  ],
  showAuthStatus: true,
})

// Query params and search
const { queryParams } = useQueryParams()
const { search, nextPage, prevPage, goToPage } = useBaseQuery()

// Local search state for debouncing
const searchQuery = ref((queryParams.value.q as string) || '')

// Watch for search input changes with debouncing
watch(searchQuery, (newValue) => {
  const timeoutId = setTimeout(() => {
    search(newValue)
  }, 300)

  return () => clearTimeout(timeoutId)
}, { immediate: false })

// Data fetching
const { data, pending: loading, error, refresh: fetchAssessments } = useAsyncData(
  'assessmentsList',
  () => assessmentRepository.listAssessments({
    per_page: 15,
  }),
  {
    watch: [queryParams],
  },
)

// Computed properties
const assessments = computed(() => data.value?.data || [])
const pagination = computed(() => data.value)

// Helper functions
function getCorrectAnswers(assessment: Assessment): string[] {
  return assessment.correct_answer_indexes.map(index => assessment.answer_list[index])
}

function getAttachedUnitsCount(assessment: Assessment): number {
  return assessment.assessment?.units?.length || 0
}

// Actions
const { open: openModal } = useModal()

async function handleDeleteAssessment(assessment: Assessment) {
  const confirmed = await openModal({
    title: 'Delete Assessment',
    description: `Are you sure you want to delete this assessment? This action cannot be undone.`,
    variant: 'destructive',
    buttons: [
      { text: 'Cancel', variant: 'outline' },
      { text: 'Delete', variant: 'destructive' },
    ],
  })

  if (confirmed) {
    try {
      await assessmentRepository.deleteAssessment(assessment.id)
      await fetchAssessments()
      // Show success toast
    } catch (error: any) {
      console.error('Failed to delete assessment:', error)
      // Show error toast
    }
  }
}

// Page meta
useHead({
  title: 'Assessment Management | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Manage assessments for the English Learning Game. Create, edit, and organize quiz questions.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Assessment Management
        </h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Create and manage quiz questions and assessments
        </p>
      </div>
      <Button as-child>
        <NuxtLink to="/admin/assessments/create">
          <Plus class="w-4 h-4 mr-2" />
          Create Assessment
        </NuxtLink>
      </Button>
    </div>

    <!-- Search -->
    <Card>
      <CardContent class="p-6">
        <div class="relative">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            v-model="searchQuery"
            placeholder="Search assessments..."
            class="pl-10"
          />
        </div>
      </CardContent>
    </Card>

    <!-- Assessments Table -->
    <Card>
      <CardHeader>
        <CardTitle>Assessments</CardTitle>
        <CardDescription>
          {{ pagination?.total || 0 }} assessments total
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div v-if="loading" class="flex items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        </div>

        <div v-else-if="error" class="text-center py-8">
          <p class="text-red-600 dark:text-red-400">Failed to load assessments</p>
          <Button variant="outline" @click="fetchAssessments" class="mt-2">
            Try Again
          </Button>
        </div>

        <div v-else-if="assessments.length === 0" class="text-center py-8">
          <HelpCircle class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-600 dark:text-gray-300">No assessments found</p>
          <Button as-child class="mt-4">
            <NuxtLink to="/admin/assessments/create">
              Create Your First Assessment
            </NuxtLink>
          </Button>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="assessment in assessments"
            :key="assessment.id"
            class="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <!-- Question -->
                <h3 class="font-semibold text-gray-900 dark:text-white mb-3">
                  {{ assessment.question }}
                </h3>

                <!-- Answer Options -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3">
                  <div
                    v-for="(option, index) in assessment.answer_list"
                    :key="index"
                    class="flex items-center gap-2 p-2 rounded border"
                    :class="{
                      'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800': assessment.correct_answer_indexes.includes(index),
                      'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700': !assessment.correct_answer_indexes.includes(index)
                    }"
                  >
                    <span class="text-sm font-medium text-gray-500">
                      {{ String.fromCharCode(65 + index) }}.
                    </span>
                    <span class="text-sm">{{ option }}</span>
                    <CheckCircle
                      v-if="assessment.correct_answer_indexes.includes(index)"
                      class="w-4 h-4 text-green-600 ml-auto"
                    />
                  </div>
                </div>

                <!-- Explanation -->
                <div v-if="assessment.explanations?.length" class="mb-3">
                  <p class="text-sm text-gray-600 dark:text-gray-300">
                    <span class="font-medium">Explanation:</span>
                    {{ assessment.explanations.join(', ') }}
                  </p>
                </div>

                <!-- Metadata -->
                <div class="flex items-center gap-4 text-xs text-gray-500">
                  <span>{{ getAttachedUnitsCount(assessment) }} units attached</span>
                  <span v-if="assessment.created_at">
                    Created {{ new Date(assessment.created_at).toLocaleDateString() }}
                  </span>
                </div>
              </div>

              <!-- Actions -->
              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal class="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem as-child>
                    <NuxtLink :to="`/admin/assessments/${assessment.id}`">
                      <Edit class="w-4 h-4 mr-2" />
                      Edit
                    </NuxtLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem as-child>
                    <NuxtLink :to="`/admin/assessments/${assessment.id}/units`">
                      <Link class="w-4 h-4 mr-2" />
                      Manage Units
                    </NuxtLink>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    @click="handleDeleteAssessment(assessment)"
                    class="text-red-600 focus:text-red-600"
                  >
                    <Trash2 class="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Pagination -->
    <div v-if="pagination && pagination.total > pagination.per_page" class="flex items-center justify-between">
      <p class="text-sm text-gray-600 dark:text-gray-300">
        Showing {{ pagination.from }} to {{ pagination.to }} of {{ pagination.total }} assessments
      </p>

      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.current_page <= 1"
          @click="prevPage"
        >
          Previous
        </Button>

        <span class="text-sm text-gray-600 dark:text-gray-300">
          Page {{ pagination.current_page }} of {{ pagination.last_page }}
        </span>

        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.current_page >= pagination.last_page"
          @click="nextPage"
        >
          Next
        </Button>
      </div>
    </div>
  </div>
</template>
