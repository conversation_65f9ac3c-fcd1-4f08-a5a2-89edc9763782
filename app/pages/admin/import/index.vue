<script setup lang="ts">
import { ref, computed } from 'vue'
import { Upload, Download, FileText, AlertCircle, CheckCircle, ArrowLeft } from 'lucide-vue-next'
import { csvImportRepository } from '@/repositories/csvImport'
import { courseRepository } from '@/repositories/course'
import type { ValidationResponse, ImportResponse } from '@/types/course'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'CSV Import',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'CSV Import', active: true },
  ],
  showAuthStatus: true,
})

// State
const selectedFile = ref<File | null>(null)
const selectedCourseId = ref<number | null>(null)
const validationResult = ref<ValidationResponse | null>(null)
const importResult = ref<ImportResponse | null>(null)
const loading = ref(false)
const validating = ref(false)
const errors = ref<string[]>([])

// Fetch courses for selection
const { data: coursesData } = useAsyncData(
  'coursesForImport',
  () => courseRepository.listCourses({ per_page: 100 }),
)

const courses = computed(() => coursesData.value?.data || [])

// File handling
function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    const validation = csvImportRepository.validateFile(file)
    if (!validation.isValid) {
      errors.value = [validation.error!]
      selectedFile.value = null
      return
    }
    
    selectedFile.value = file
    validationResult.value = null
    importResult.value = null
    errors.value = []
  }
}

// Validation
async function validateCSV() {
  if (!selectedFile.value || !selectedCourseId.value) {
    errors.value = ['Please select both a file and a course']
    return
  }
  
  validating.value = true
  errors.value = []
  
  try {
    validationResult.value = await csvImportRepository.validateCSV(
      selectedFile.value,
      selectedCourseId.value
    )
  } catch (error: any) {
    console.error('Validation failed:', error)
    errors.value = [error.message || 'Validation failed']
  } finally {
    validating.value = false
  }
}

// Import
async function importCSV() {
  if (!selectedFile.value || !selectedCourseId.value) {
    errors.value = ['Please select both a file and a course']
    return
  }
  
  loading.value = true
  errors.value = []
  
  try {
    importResult.value = await csvImportRepository.importCSV(
      selectedFile.value,
      selectedCourseId.value
    )
    
    // Clear form after successful import
    selectedFile.value = null
    selectedCourseId.value = null
    validationResult.value = null
  } catch (error: any) {
    console.error('Import failed:', error)
    errors.value = [error.message || 'Import failed']
  } finally {
    loading.value = false
  }
}

// Download template
async function downloadTemplate() {
  try {
    await csvImportRepository.downloadTemplateFile()
  } catch (error: any) {
    console.error('Failed to download template:', error)
    errors.value = ['Failed to download template']
  }
}

// Reset form
function resetForm() {
  selectedFile.value = null
  selectedCourseId.value = null
  validationResult.value = null
  importResult.value = null
  errors.value = []
}

// Page meta
useHead({
  title: 'CSV Import | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Import course content from CSV files into the English Learning Game.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div class="flex items-center gap-4">
      <div class="flex-1">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          CSV Import
        </h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Import course content from CSV files
        </p>
      </div>
      
      <Button variant="outline" @click="downloadTemplate">
        <Download class="w-4 h-4 mr-2" />
        Download Template
      </Button>
    </div>

    <!-- Import Success -->
    <div v-if="importResult" class="space-y-6">
      <Card class="border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-800">
        <CardContent class="p-6">
          <div class="flex items-center gap-3 mb-4">
            <CheckCircle class="w-6 h-6 text-green-600" />
            <h3 class="text-lg font-semibold text-green-800 dark:text-green-200">
              Import Completed Successfully!
            </h3>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="text-center p-4 bg-white dark:bg-gray-800 rounded-lg">
              <div class="text-2xl font-bold text-green-600">
                {{ importResult.summary.units_created }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-300">
                Units Created
              </div>
            </div>
            <div class="text-center p-4 bg-white dark:bg-gray-800 rounded-lg">
              <div class="text-2xl font-bold text-blue-600">
                {{ importResult.summary.assessments_created }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-300">
                Assessments Created
              </div>
            </div>
          </div>
          
          <div class="flex items-center gap-4">
            <Button @click="resetForm">
              Import Another File
            </Button>
            <Button variant="outline" as-child>
              <NuxtLink to="/admin/courses">
                View Courses
              </NuxtLink>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Import Form -->
    <div v-else class="max-w-2xl space-y-6">
      <!-- Errors -->
      <div v-if="errors.length" class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex items-center gap-2 mb-2">
          <AlertCircle class="w-5 h-5 text-red-600" />
          <h4 class="font-medium text-red-800">Import Errors</h4>
        </div>
        <ul class="list-disc list-inside text-red-700 space-y-1">
          <li v-for="error in errors" :key="error">{{ error }}</li>
        </ul>
      </div>

      <!-- File Selection -->
      <Card>
        <CardHeader>
          <CardTitle>Select CSV File</CardTitle>
          <CardDescription>
            Choose a CSV file containing course content to import
          </CardDescription>
        </CardHeader>
        
        <CardContent class="space-y-4">
          <!-- Course Selection -->
          <div class="space-y-2">
            <Label for="course">Target Course *</Label>
            <Select v-model="selectedCourseId">
              <SelectTrigger>
                <SelectValue placeholder="Select a course" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="course in courses"
                  :key="course.id"
                  :value="course.id"
                >
                  {{ course.title }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- File Input -->
          <div class="space-y-2">
            <Label for="file">CSV File *</Label>
            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
              <input
                id="file"
                type="file"
                accept=".csv,.txt"
                class="hidden"
                @change="handleFileSelect"
              />
              <label
                for="file"
                class="cursor-pointer flex flex-col items-center gap-2"
              >
                <Upload class="w-8 h-8 text-gray-400" />
                <span class="text-sm text-gray-600 dark:text-gray-300">
                  Click to select a CSV file
                </span>
                <span class="text-xs text-gray-500">
                  Maximum file size: 10MB
                </span>
              </label>
            </div>
            
            <div v-if="selectedFile" class="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <FileText class="w-4 h-4 text-gray-600" />
              <span class="text-sm text-gray-900 dark:text-white">
                {{ selectedFile.name }}
              </span>
              <span class="text-xs text-gray-500 ml-auto">
                {{ (selectedFile.size / 1024).toFixed(1) }} KB
              </span>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center gap-4 pt-4">
            <Button
              @click="validateCSV"
              :disabled="!selectedFile || !selectedCourseId || validating"
              variant="outline"
            >
              <div v-if="validating" class="flex items-center gap-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                <span>Validating...</span>
              </div>
              <span v-else>Validate File</span>
            </Button>
            
            <Button
              @click="importCSV"
              :disabled="!validationResult || loading"
            >
              <div v-if="loading" class="flex items-center gap-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                <span>Importing...</span>
              </div>
              <span v-else>Import Data</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Validation Results -->
      <Card v-if="validationResult">
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <CheckCircle class="w-5 h-5 text-green-600" />
            Validation Successful
          </CardTitle>
          <CardDescription>
            Your CSV file is valid and ready for import
          </CardDescription>
        </CardHeader>
        
        <CardContent class="space-y-4">
          <!-- Summary -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-xl font-bold text-gray-900 dark:text-white">
                {{ validationResult.total_rows }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-300">
                Total Rows
              </div>
            </div>
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-xl font-bold text-blue-600">
                {{ validationResult.estimated_units }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-300">
                Units to Create
              </div>
            </div>
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="text-xl font-bold text-green-600">
                {{ validationResult.estimated_assessments }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-300">
                Assessments to Create
              </div>
            </div>
          </div>

          <!-- Preview -->
          <div v-if="validationResult.preview?.length">
            <h4 class="font-medium text-gray-900 dark:text-white mb-2">
              Data Preview
            </h4>
            <div class="border rounded-lg overflow-hidden">
              <div class="bg-gray-50 dark:bg-gray-800 p-3 border-b">
                <div class="grid grid-cols-4 gap-4 text-xs font-medium text-gray-600 dark:text-gray-300">
                  <span>Unit</span>
                  <span>Skill Type</span>
                  <span>Question</span>
                  <span>Correct Answer</span>
                </div>
              </div>
              <div class="max-h-48 overflow-y-auto">
                <div
                  v-for="(row, index) in validationResult.preview.slice(0, 5)"
                  :key="index"
                  class="p-3 border-b last:border-b-0"
                >
                  <div class="grid grid-cols-4 gap-4 text-sm">
                    <span class="truncate">{{ row.unit_title }}</span>
                    <span>{{ row.skill_type }}</span>
                    <span class="truncate">{{ row.question }}</span>
                    <span class="truncate">{{ row.correct_answer }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
