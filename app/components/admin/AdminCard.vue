<script setup lang="ts">
interface Props {
  title?: string
  description?: string
  showHeader?: boolean
  padding?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'bordered' | 'elevated'
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: false,
  padding: 'md',
  variant: 'default',
})

const paddingClasses = {
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
}

const variantClasses = {
  default: 'bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm',
  bordered: 'bg-white dark:bg-gray-800 rounded-xl border-2 border-gray-200 dark:border-gray-700',
  elevated: 'bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg',
}
</script>

<template>
  <div :class="variantClasses[variant]">
    <!-- Header -->
    <div 
      v-if="showHeader && (title || description || $slots.header)"
      class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"
    >
      <slot name="header">
        <h2 v-if="title" class="text-lg font-semibold text-gray-900 dark:text-white">
          {{ title }}
        </h2>
        <p v-if="description" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {{ description }}
        </p>
      </slot>
    </div>

    <!-- Content -->
    <div :class="paddingClasses[padding]">
      <slot />
    </div>

    <!-- Footer -->
    <div 
      v-if="$slots.footer"
      class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50 rounded-b-xl"
    >
      <slot name="footer" />
    </div>
  </div>
</template>
