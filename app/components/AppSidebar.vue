<script setup lang="ts">
import { Home, BarChart3, Settings, Users, BookOpen, Trophy } from 'lucide-vue-next'

// Sample navigation items - customize based on your needs
const navigationItems = [
  {
    title: 'Dashboard',
    icon: Home,
    href: '/dashboard',
    active: true
  },
  {
    title: 'Game Management',
    icon: BookOpen,
    href: '/dashboard/games',
    active: false
  },
  {
    title: 'Statistics',
    icon: BarChart3,
    href: '/dashboard/stats',
    active: false
  },
  {
    title: 'Users',
    icon: Users,
    href: '/dashboard/users',
    active: false
  },
  {
    title: 'Achievements',
    icon: Trophy,
    href: '/dashboard/achievements',
    active: false
  },
  {
    title: 'Settings',
    icon: Settings,
    href: '/dashboard/settings',
    active: false
  }
]
</script>

<template>
  <Sidebar>
    <SidebarHeader>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" as-child>
            <NuxtLink to="/dashboard">
              <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <BookOpen class="size-4" />
              </div>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">English Game</span>
                <span class="truncate text-xs">Admin Dashboard</span>
              </div>
            </NuxtLink>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>

    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel>Navigation</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem v-for="item in navigationItems" :key="item.title">
              <SidebarMenuButton as-child :is-active="item.active">
                <NuxtLink :to="item.href">
                  <component :is="item.icon" class="size-4" />
                  <span>{{ item.title }}</span>
                </NuxtLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>

    <SidebarFooter>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" as-child>
            <NuxtLink to="/">
              <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-muted">
                <Home class="size-4" />
              </div>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">Back to Game</span>
                <span class="truncate text-xs">Return to main site</span>
              </div>
            </NuxtLink>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarFooter>
  </Sidebar>
</template>
