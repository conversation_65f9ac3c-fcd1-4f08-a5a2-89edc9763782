import { getQuestionsForLevel, getQuestionById, submitAnswer, getGameStructure } from '@/repositories/questionRepository'
import type { LevelQuestions, ProcessedQuestion } from '@/types/question'

/**
 * Questions composable following the API client guide pattern
 * Provides SSR-friendly data fetching and client-side operations
 */
export function useQuestions() {
  
  /**
   * Fetch questions for a specific stage and level
   * Uses useAsyncData for SSR compatibility and caching
   */
  function fetchQuestionsForLevel(stage: number, level: number) {
    return useAsyncData(
      `questions-${stage}-${level}`,
      () => getQuestionsForLevel(stage, level),
      {
        key: `questions-${stage}-${level}`,
        server: true,
        default: () => ({
          stage,
          level,
          questions: []
        } as LevelQuestions)
      }
    )
  }

  /**
   * Lazy fetch questions for a specific stage and level
   * Won't block navigation, loads after page renders
   */
  function fetchQuestionsForLevelLazy(stage: number, level: number) {
    return useLazyAsyncData(
      `questions-lazy-${stage}-${level}`,
      () => getQuestionsForLevel(stage, level),
      {
        server: false,
        default: () => ({
          stage,
          level,
          questions: []
        } as LevelQuestions)
      }
    )
  }

  /**
   * Fetch a specific question by ID
   * Uses useAsyncData for caching
   */
  function fetchQuestionById(questionId: number) {
    return useAsyncData(
      `question-${questionId}`,
      () => getQuestionById(questionId),
      {
        key: `question-${questionId}`,
        server: true
      }
    )
  }

  /**
   * Submit answer for a question (client-side operation)
   * Uses the repository method directly for user interactions
   */
  async function submitQuestionAnswer(
    questionId: number,
    selectedAnswerIndex: number,
    isCorrect: boolean,
    stage: number,
    level: number
  ) {
    try {
      return await submitAnswer(questionId, selectedAnswerIndex, isCorrect, stage, level)
    } catch (error: any) {
      throw new Error(`Failed to submit answer: ${error.message}`)
    }
  }

  /**
   * Fetch game structure (stages and levels)
   * Uses useAsyncData for caching
   */
  function fetchGameStructure() {
    return useAsyncData(
      'game-structure',
      () => getGameStructure(),
      {
        key: 'game-structure',
        server: true,
        default: () => ({
          stages: []
        })
      }
    )
  }

  /**
   * Get questions for level with reactive params
   * Automatically refetches when stage or level changes
   */
  function useQuestionsForLevel(stage: Ref<number>, level: Ref<number>) {
    return useAsyncData(
      'questions-reactive',
      () => getQuestionsForLevel(stage.value, level.value),
      {
        key: computed(() => `questions-${stage.value}-${level.value}`),
        watch: [stage, level],
        server: true,
        default: () => ({
          stage: stage.value,
          level: level.value,
          questions: []
        } as LevelQuestions)
      }
    )
  }

  /**
   * Utility for managing loading states in components
   */
  function useQuestionState() {
    const { loading, error, data, execute, reset } = useAPIState()

    const loadQuestions = (stage: number, level: number) => {
      return execute(() => getQuestionsForLevel(stage, level))
    }

    const loadQuestion = (questionId: number) => {
      return execute(() => getQuestionById(questionId))
    }

    const submitQuestionAnswerWithState = (
      questionId: number,
      selectedAnswerIndex: number,
      isCorrect: boolean,
      stage: number,
      level: number
    ) => {
      return execute(() => submitAnswer(questionId, selectedAnswerIndex, isCorrect, stage, level))
    }

    return {
      loading,
      error,
      data,
      reset,
      loadQuestions,
      loadQuestion,
      submitQuestionAnswerWithState
    }
  }

  return {
    // SSR-friendly data fetching
    fetchQuestionsForLevel,
    fetchQuestionsForLevelLazy,
    fetchQuestionById,
    fetchGameStructure,
    
    // Reactive data fetching
    useQuestionsForLevel,
    
    // Client-side operations
    submitQuestionAnswer,
    
    // Utility
    useQuestionState
  }
}