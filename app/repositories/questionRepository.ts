import type { LevelQuestions, ProcessedQuestion, Question } from '@/types/question'

// Mock API data as provided
const MOCK_QUESTIONS: Question[] = [
  {
    'Question': 'Paraphrase cho "a significant increase". Which phrase is NOT an appropriate synonym?',
    'answer list': [
      'a dramatic rise',
      'a notable growth',
      'a steady routine',
      'a considerable surge',
    ],
    'Correct Answer index Array': [2],
    'explain': [
      '"Routine" không mang nghĩa tăng trưởng nên đáp án C ("a steady routine") không phù hợp.',
    ],
  },
  {
    'Question': 'Paraphrase cho "advantages". Which phrase does NOT belong with the others?',
    'answer list': [
      'positive outcomes',
      'key benefits',
      'potential drawbacks',
      'favorable impacts',
    ],
    'Correct Answer index Array': [2],
    'explain': [
      '"Drawbacks" mang nghĩa trái ngược, vì vậy đáp án C ("potential drawbacks") cần loại bỏ.',
    ],
  },
  {
    'Question': 'Paraphrase cho "people believe that...". Which phrase is NOT a good paraphrase of this idea?',
    'answer list': [
      'there is a widespread belief that',
      'some individuals hold the view that',
      'the government enforces the rule that',
      'it is commonly thought that',
    ],
    'Correct Answer index Array': [2],
    'explain': [
      '"The government enforces the rule that" diễn tả việc áp đặt luật, không phải niềm tin; do đó đáp án C không phù hợp.',
    ],
  },
  {
    'Question': 'Paraphrase cho "cause problems". Which one does NOT match the meaning of the others?',
    'answer list': [
      'lead to issues',
      'trigger complications',
      'bring solutions',
      'generate concerns',
    ],
    'Correct Answer index Array': [2],
    'explain': [
      '"Bring solutions" mang nghĩa tích cực, ngược với "cause problems"; vì thế đáp án C bị loại.',
    ],
  },
  {
    'Question': 'Paraphrase cho "young people". Which phrase is NOT a suitable synonym in academic writing?',
    'answer list': [
      'the younger generation',
      'youth',
      'adolescents',
      'kids',
    ],
    'Correct Answer index Array': [3],
    'explain': [
      '"Kids" quá thông tục, không phù hợp văn phong học thuật; do đó đáp án D cần loại bỏ.',
    ],
  },
]

/**
 * Transform raw API question data to processed question format
 */
function transformQuestion(question: Question, index: number): ProcessedQuestion {
  return {
    id: index + 1,
    question: question.Question,
    options: question['answer list'].map((text, optionIndex) => ({
      text,
      isCorrect: question['Correct Answer index Array'].includes(optionIndex),
    })),
    explanation: question.explain[0] || 'No explanation provided.',
    correctAnswers: question['Correct Answer index Array'],
  }
}

/**
 * Simulate API delay for realistic behavior
 */
function simulateApiDelay(ms: number = 500): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Get questions for a specific stage and level
 * Uses mock data for now, but in production would call:
 * GET /api/questions/stage/{stage}/level/{level}
 */
export async function getQuestionsForLevel(stage: number, level: number): Promise<LevelQuestions> {
  try {
    await simulateApiDelay()

    // In a real app, this would be an actual API call:
    // const { get } = useAPICall()
    // const response = await get<Question[]>(`/questions/stage/${stage}/level/${level}`)

    // For now, use mock data for all levels
    const questions = MOCK_QUESTIONS.map((q, index) => transformQuestion(q, index))

    const result: LevelQuestions = {
      stage,
      level,
      questions,
    }

    return result
  }
  catch (error: any) {
    throw new Error(`Failed to fetch questions for stage ${stage}, level ${level}: ${error.message || error}`)
  }
}

/**
 * Get a specific question by ID
 * Uses mock data for now, but in production would call:
 * GET /api/questions/{questionId}
 */
export async function getQuestionById(questionId: number): Promise<ProcessedQuestion> {
  try {
    await simulateApiDelay(200)

    // In a real app, this would be an actual API call:
    // const { get } = useAPICall()
    // const response = await get<Question>(`/questions/${questionId}`)

    const question = MOCK_QUESTIONS.find((_, index) => index + 1 === questionId)

    if (!question) {
      throw new Error(`Question with ID ${questionId} not found`)
    }

    return transformQuestion(question, questionId - 1)
  }
  catch (error: any) {
    throw new Error(`Failed to fetch question ${questionId}: ${error.message || error}`)
  }
}

/**
 * Submit answer for a question (for analytics/progress tracking)
 * Uses mock response for now, but in production would call:
 * POST /api/answers
 */
export async function submitAnswer(
  questionId: number,
  selectedAnswerIndex: number,
  isCorrect: boolean,
  stage: number,
  level: number,
): Promise<{ success: boolean, score: number }> {
  try {
    await simulateApiDelay(300)

    // In a real app, this would track user progress and analytics:
    // const { post } = useAPICall()
    // const response = await post<{ success: boolean; score: number }>('/answers', {
    //   questionId,
    //   selectedAnswerIndex,
    //   isCorrect,
    //   stage,
    //   level
    // })

    const scoreIncrement = isCorrect ? 10 : 0

    return {
      success: true,
      score: scoreIncrement,
    }
  }
  catch (error: any) {
    throw new Error(`Failed to submit answer: ${error.message || error}`)
  }
}

/**
 * Get available stages and levels
 * Uses mock data for now, but in production would call:
 * GET /api/game/structure
 */
export async function getGameStructure(): Promise<{ stages: Array<{ id: number, levels: number[] }> }> {
  try {
    await simulateApiDelay(200)

    // In a real app, this would come from API:
    // const { get } = useAPICall()
    // const response = await get<{ stages: Array<{ id: number; levels: number[] }> }>('/game/structure')

    // Mock game structure
    return {
      stages: [
        { id: 1, levels: [1, 2, 3, 4, 5] },
        { id: 2, levels: [1, 2, 3, 4, 5] },
        { id: 3, levels: [1, 2, 3, 4, 5] },
      ],
    }
  }
  catch (error: any) {
    throw new Error(`Failed to fetch game structure: ${error.message || error}`)
  }
}

// Export the mock data for testing purposes
export { MOCK_QUESTIONS }
