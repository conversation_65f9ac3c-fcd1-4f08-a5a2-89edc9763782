import type { 
  Assessment, 
  CreateAssessmentData, 
  UpdateAssessmentData, 
  AssessmentListQuery, 
  AssessmentListResponse,
  AttachUnitsData,
  DetachUnitsData
} from '@/types/course'

/**
 * Assessment Management Repository
 * Pure API calls for assessment management endpoints
 * Handles both admin and public assessment operations
 */
export const assessmentRepository = {
  /**
   * List all multiple-select assessments (Admin)
   * @param query - Query parameters for filtering and pagination
   * @returns Promise resolving to paginated assessment list
   */
  async listAssessments(query?: AssessmentListQuery): Promise<AssessmentListResponse> {
    const { call } = useAPICall()
    return await call<AssessmentListResponse>('/api/admin/assessments/multiple-select', {
      method: 'GET',
      query,
    })
  },

  /**
   * Create a new multiple-select assessment (Admin)
   * @param assessmentData - Assessment creation data
   * @returns Promise resolving to created assessment
   */
  async createAssessment(assessmentData: CreateAssessmentData): Promise<Assessment> {
    const { call } = useAPICall()
    return await call<Assessment>('/api/admin/assessments/multiple-select', {
      method: 'POST',
      body: assessmentData,
    })
  },

  /**
   * Get assessment details (Admin)
   * @param id - Assessment ID
   * @returns Promise resolving to assessment details
   */
  async getAssessment(id: number): Promise<Assessment> {
    const { call } = useAPICall()
    return await call<Assessment>(`/api/admin/assessments/multiple-select/${id}`, {
      method: 'GET',
    })
  },

  /**
   * Get assessment details (Public)
   * @param id - Assessment ID
   * @returns Promise resolving to assessment details
   */
  async getPublicAssessment(id: number): Promise<Assessment> {
    const { call } = useAPICall()
    return await call<Assessment>(`/api/public/assessments/multiple-select/${id}`, {
      method: 'GET',
    })
  },

  /**
   * Update assessment (Admin)
   * @param id - Assessment ID
   * @param assessmentData - Assessment update data
   * @returns Promise resolving to updated assessment
   */
  async updateAssessment(id: number, assessmentData: UpdateAssessmentData): Promise<Assessment> {
    const { call } = useAPICall()
    return await call<Assessment>(`/api/admin/assessments/multiple-select/${id}`, {
      method: 'PUT',
      body: assessmentData,
    })
  },

  /**
   * Partially update assessment (Admin)
   * @param id - Assessment ID
   * @param assessmentData - Assessment update data
   * @returns Promise resolving to updated assessment
   */
  async patchAssessment(id: number, assessmentData: UpdateAssessmentData): Promise<Assessment> {
    const { call } = useAPICall()
    return await call<Assessment>(`/api/admin/assessments/multiple-select/${id}`, {
      method: 'PATCH',
      body: assessmentData,
    })
  },

  /**
   * Delete assessment (Admin)
   * @param id - Assessment ID
   * @returns Promise resolving to deletion response
   */
  async deleteAssessment(id: number): Promise<void> {
    const { call } = useAPICall()
    return await call<void>(`/api/admin/assessments/multiple-select/${id}`, {
      method: 'DELETE',
    })
  },

  /**
   * Attach assessment to units (Admin)
   * @param id - Assessment ID
   * @param attachData - Unit attachment data
   * @returns Promise resolving to attachment response
   */
  async attachAssessmentToUnits(id: number, attachData: AttachUnitsData): Promise<Assessment> {
    const { call } = useAPICall()
    return await call<Assessment>(`/api/admin/assessments/multiple-select/${id}/attach-units`, {
      method: 'POST',
      body: attachData,
    })
  },

  /**
   * Detach assessment from units (Admin)
   * @param id - Assessment ID
   * @param detachData - Unit detachment data
   * @returns Promise resolving to detachment response
   */
  async detachAssessmentFromUnits(id: number, detachData: DetachUnitsData): Promise<Assessment> {
    const { call } = useAPICall()
    return await call<Assessment>(`/api/admin/assessments/multiple-select/${id}/detach-units`, {
      method: 'POST',
      body: detachData,
    })
  },

  /**
   * Detach assessment from all units (Admin)
   * @param id - Assessment ID
   * @returns Promise resolving to detachment response
   */
  async detachAssessmentFromAllUnits(id: number): Promise<Assessment> {
    const { call } = useAPICall()
    return await call<Assessment>(`/api/admin/assessments/multiple-select/${id}/detach-units`, {
      method: 'POST',
      body: { unit_ids: [] },
    })
  },
}
