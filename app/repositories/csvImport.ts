import type { 
  ImportResponse, 
  ValidationResponse
} from '@/types/course'

/**
 * CSV Import Repository
 * Pure API calls for CSV import functionality
 * Handles file uploads and validation for course content import
 */
export const csvImportRepository = {
  /**
   * Import CSV file (Admin)
   * @param csvFile - CSV file to import
   * @param courseId - Target course ID
   * @returns Promise resolving to import results
   */
  async importCSV(csvFile: File, courseId: number): Promise<ImportResponse> {
    const { call } = useAPICall()
    
    const formData = new FormData()
    formData.append('csv_file', csvFile)
    formData.append('course_id', courseId.toString())

    return await call<ImportResponse>('/api/admin/import/csv', {
      method: 'POST',
      body: formData,
    })
  },

  /**
   * Validate CSV file without importing (Admin)
   * @param csvFile - CSV file to validate
   * @param courseId - Target course ID
   * @returns Promise resolving to validation results
   */
  async validateCSV(csvFile: File, courseId: number): Promise<ValidationResponse> {
    const { call } = useAPICall()
    
    const formData = new FormData()
    formData.append('csv_file', csvFile)
    formData.append('course_id', courseId.toString())

    return await call<ValidationResponse>('/api/admin/import/validate-csv', {
      method: 'POST',
      body: formData,
    })
  },

  /**
   * Download CSV template (Admin)
   * @returns Promise resolving to CSV template file
   */
  async downloadTemplate(): Promise<Blob> {
    const { call } = useAPICall()
    
    // Note: This endpoint returns a file download, so we need to handle it differently
    // The response will be a Blob that can be used to create a download link
    return await call<Blob>('/api/admin/import/template', {
      method: 'GET',
    })
  },

  /**
   * Helper method to create a download link for the CSV template
   * @returns Promise that triggers the template download
   */
  async downloadTemplateFile(): Promise<void> {
    try {
      const blob = await this.downloadTemplate()
      
      // Create a temporary URL for the blob
      const url = window.URL.createObjectURL(blob)
      
      // Create a temporary anchor element and trigger download
      const link = document.createElement('a')
      link.href = url
      link.download = 'import_template.csv'
      document.body.appendChild(link)
      link.click()
      
      // Clean up
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to download template:', error)
      throw error
    }
  },

  /**
   * Helper method to validate file before upload
   * @param file - File to validate
   * @returns Boolean indicating if file is valid
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['text/csv', 'text/plain', 'application/csv']
    const allowedExtensions = ['.csv', '.txt']
    
    const hasValidType = allowedTypes.includes(file.type)
    const hasValidExtension = allowedExtensions.some(ext => 
      file.name.toLowerCase().endsWith(ext)
    )
    
    if (!hasValidType && !hasValidExtension) {
      return {
        isValid: false,
        error: 'Invalid file type. Please upload a CSV or TXT file.'
      }
    }
    
    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB in bytes
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size exceeds 10MB limit.'
      }
    }
    
    return { isValid: true }
  },
}
