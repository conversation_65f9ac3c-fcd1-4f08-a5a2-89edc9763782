import { TOKEN_AUTH_KEY } from '#shared/constants'

export default defineNuxtPlugin(() => {
  console.log('Universal API plugin loaded')
  const config = useRuntimeConfig()

  // Create a custom $fetch instance for direct API calls
  const api = $fetch.create({
    baseURL: config.public.apiBaseUrl as string,

    // Request interceptor
    onRequest({ request, options }) {
      // Get token from cookies (works on both client and server)
      const tokenCookie = useCookie(TOKEN_AUTH_KEY, {
        default: () => '',
        secure: true,
        sameSite: 'strict',
        httpOnly: false, // Allow client-side access
      })

      const token = tokenCookie.value || null

      // Add authentication header if token exists
      if (token) {
        setHeaders(options.headers, 'Authorization', `Bearer ${token}`)
      }

      // Add common headers
      setHeaders(options.headers, 'Content-Type', 'application/json')
      setHeaders(options.headers, 'Accept', 'application/json')

      // Log request in development
      if (import.meta.dev) {
        console.log(`🚀 API Request: ${options.method || 'GET'} ${request}`)
      }
    },

    // Response interceptor
    onResponse({ request, response, options }) {
      // Log response in development
      if (import.meta.dev) {
        console.log(`✅ API Response: ${response.status} ${request}`)
      }

      // Extract and store new token if provided
      const newToken = response.headers.get('X-New-Token')
      if (newToken) {
        const tokenCookie = useCookie(TOKEN_AUTH_KEY, {
          default: () => '',
          secure: true,
          sameSite: 'strict',
          httpOnly: false,
        })
        tokenCookie.value = newToken
      }
    },

    // Error interceptor
    onResponseError({ request, response, options }) {
      const errorData: ApiError = {
        message: response._data?.message || 'An error occurred',
        statusCode: response.status,
        data: response._data,
      }

      // Handle specific error types
      switch (response.status) {
        case 401: {
          // Unauthorized - clear token and redirect to login
          const tokenCookie = useCookie(TOKEN_AUTH_KEY, {
            default: () => '',
            secure: true,
            sameSite: 'strict',
            httpOnly: false,
          })
          tokenCookie.value = ''

          // Only handle navigation on client side
          if (import.meta.client) {
            // You can add navigation logic here
            // navigateTo('/login')
          }
          errorData.message = 'Authentication required'
          break
        }

        case 403:
          errorData.message = 'Access forbidden'
          break

        case 404:
          errorData.message = 'Resource not found'
          break

        case 422:
          errorData.message = 'Validation error'
          errorData.errors = response._data?.errors
          break

        case 429:
          errorData.message = 'Too many requests'
          break

        case 500:
        case 502:
        case 503:
        case 504:
          errorData.message = 'Server error'
          break

        default:
          errorData.message = response._data?.message || 'An unexpected error occurred'
      }

      // Log error in development
      if (import.meta.dev) {
        console.error(`❌ API Error: ${response.status} ${request}`, errorData)
      }

      // Throw a structured error
      throw createError({
        statusCode: response.status,
        statusMessage: errorData.message,
        data: errorData,
      })
    },

    // Retry configuration
    retry: 1,
    retryDelay: 500,
    timeout: 10000, // 10 seconds
  })

  // Provide the API instance globally
  return {
    provide: {
      api,
    },
  }
})

function setHeaders(headers: any, key: string, value: string) {
  if (Array.isArray(headers)) {
    headers.push([key, value])
  }
  else if (headers instanceof Headers) {
    headers.set(key, value)
  }
  else {
    headers[key] = value
  }
}
