import type { LaravelPaginatedResponse } from './api'

// Enums for skill types and difficulties
export type SkillType = 'vocabulary' | 'grammar' | 'listening' | 'reading' | 'speaking' | 'writing'
export type Difficulty = 'beginner' | 'elementary' | 'intermediate' | 'upper_intermediate' | 'advanced' | 'proficient'

// Course Types
export interface Course {
  id: number
  title: string
  description: string
  created_at: string
  updated_at: string
  units?: Unit[]
}

export interface CreateCourseData {
  title: string
  description: string
}

export interface UpdateCourseData {
  title?: string
  description?: string
}

export interface CourseListQuery {
  per_page?: number
}

export interface CourseWithStats extends Course {
  units_count?: number
  assessments_count?: number
}

// Unit Types
export interface Unit {
  id: number
  course_id: number
  title: string
  description: string
  skill_type: SkillType
  difficulty: Difficulty
  unit_order: number
  created_at?: string
  updated_at?: string
  course?: Pick<Course, 'id' | 'title'>
  assessments?: Assessment[]
}

export interface CreateUnitData {
  course_id: number
  title: string
  description: string
  skill_type: SkillType
  difficulty: Difficulty
  unit_order: number
}

export interface UpdateUnitData {
  title?: string
  description?: string
  skill_type?: SkillType
  difficulty?: Difficulty
  unit_order?: number
}

export interface UnitListQuery {
  course_id?: number
  skill_type?: SkillType
  difficulty?: Difficulty
  per_page?: number
}

export interface MoveUnitData {
  target_course_id: number
  target_order: number
}

export interface ReorderUnitData {
  new_order: number
}

export interface DuplicateUnitData {
  target_order: number
}

// Assessment Types
export interface Assessment {
  id: number
  question: string
  answer_list: string[]
  correct_answer_indexes: number[]
  explanations: string[]
  created_at?: string
  updated_at?: string
  assessment?: {
    id: number
    units: Array<Pick<Unit, 'id' | 'title'>>
  }
}

export interface UnitAttachment {
  unit_id: number
  assessment_order: number
}

export interface CreateAssessmentData {
  question: string
  answer_list: string[]
  correct_answer_indexes: number[]
  explanations: string[]
  unit_attachments?: UnitAttachment[]
}

export interface UpdateAssessmentData {
  question?: string
  answer_list?: string[]
  correct_answer_indexes?: number[]
  explanations?: string[]
}

export interface AttachUnitsData {
  unit_attachments: UnitAttachment[]
}

export interface DetachUnitsData {
  unit_ids: number[]
}

export interface AssessmentListQuery {
  per_page?: number
}

// CSV Import Types
export interface CSVRow {
  unit_title: string
  unit_description?: string
  skill_type: SkillType
  difficulty: Difficulty
  question: string
  option_1: string
  option_2: string
  option_3?: string
  option_4?: string
  correct_answer: string
  explanation?: string
}

export interface ImportSummary {
  units_created: number
  assessments_created: number
}

export interface ImportResponse {
  summary: ImportSummary
  created_units: Unit[]
}

export interface ValidationResponse {
  total_rows: number
  estimated_units: number
  estimated_assessments: number
  preview: CSVRow[]
}

export interface CSVImportData {
  csv_file: File
  course_id: number
}

// Response Types
export type CourseListResponse = LaravelPaginatedResponse<Course>
export type UnitListResponse = LaravelPaginatedResponse<Unit>
export type AssessmentListResponse = LaravelPaginatedResponse<Assessment>
export type CourseStatsResponse = LaravelPaginatedResponse<CourseWithStats>
