export interface Question {
  /** The question text */
  Question: string
  
  /** Array of answer options */
  'answer list': string[]
  
  /** Array of indices for correct answers (0-based) */
  'Correct Answer index Array': number[]
  
  /** Array of explanations for the correct answer */
  explain: string[]
}

export interface ProcessedQuestion {
  /** Unique identifier for the question */
  id: number
  
  /** The question text */
  question: string
  
  /** Array of answer options with correct flag */
  options: QuestionOption[]
  
  /** Explanation for the correct answer */
  explanation: string
  
  /** Array of correct answer indices for validation */
  correctAnswers: number[]
}

export interface QuestionOption {
  /** The answer text */
  text: string
  
  /** Whether this option is correct */
  isCorrect: boolean
}

export interface LevelQuestions {
  /** Stage number */
  stage: number
  
  /** Level number */
  level: number
  
  /** Array of questions for this level */
  questions: ProcessedQuestion[]
}

export interface GameProgress {
  /** Current stage */
  currentStage: number
  
  /** Current level */
  currentLevel: number
  
  /** Current question index */
  currentQuestionIndex: number
  
  /** Player's current score */
  score: number
  
  /** Remaining lives */
  lives: number
  
  /** Statistics for the current session */
  stats: {
    totalQuestions: number
    correctAnswers: number
    wrongAnswers: number
    accuracy: number
  }
}

export interface GameState {
  /** Current question being displayed */
  currentQuestion: ProcessedQuestion | null
  
  /** All questions for the current level */
  questions: ProcessedQuestion[]
  
  /** Index of the current question */
  currentQuestionIndex: number
  
  /** Selected answer index */
  selectedAnswer: number | null
  
  /** Whether an answer has been selected */
  answerSelected: boolean
  
  /** Whether to show feedback */
  showFeedback: boolean
  
  /** Whether the selected answer is correct */
  isCorrect: boolean
  
  /** Player's current lives */
  lives: number
  
  /** Maximum lives allowed */
  maxLives: number
  
  /** Current score */
  score: number
  
  /** Loading state */
  loading: boolean
  
  /** Error state */
  error: string | null
}